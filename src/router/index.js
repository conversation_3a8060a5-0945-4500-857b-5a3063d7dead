import { createWebHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

// 公共路由
export const constantRoutes = [
  {
    path: '/',
    component: Layout,
    redirect: '/',
    hidden: true,
    children: [
      {
        path: '/',
        component: () => import('@/views/home/<USER>')
      },
      {
        path: '/chat',
        component: () => import('@/views/chat/index.vue')
      },
      {
        path: '/knowledge',
        component: () => import('@/views/knowledge/index.vue')
      },
      {
        path: '/knowledge/detail',
        name: 'KnowledgeDetail',
        component: () => import('@/components/KnowLedge/index.vue')
      },
      {
        path: '/Ai',
        component: () => import('@/views/Ai/index.vue')
      },
      {
        path: '/history',
        component: () => import('@/views/history/index.vue')
      },
      {
        path: '/setting',
        component: () => import('@/views/setting/index.vue')
      },
      {
        path: '/agent',
        redirect: '',
        children: [{
          path: '',
          component: () => import('@/views/agent/index.vue')
        }, {
          path: 'chat',
          component: () => import('@/views/agent/chat.vue')
        }]
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/login.vue'),
    hidden: true
  }
]

export const dynamicRoutes = []

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router
