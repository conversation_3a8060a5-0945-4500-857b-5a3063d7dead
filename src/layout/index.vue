<template>
  <div class="wh-full flex">
    <side-bar />
    <div class="wh-full flex flex-col flex-1">
      <!-- <header-bar bgi-[/images/home_bg_new.png]/> -->
      <div class="wh-full flex-1 overflow-hidden  p-1 bg-no-repeat bg-cover bgi-[/images/home_bg_new.jpg]">
        <div class="wh-full border-rd-1px pl-64px">
          <router-view />
        </div>
      </div>
    </div>
    <login-dialog
      v-model:visible="loginVisible"
      @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup name="Layout">
import SideBar from './components/SideBar'
// import HeaderBar from './components/HeaderBar'
import LoginDialog from '@/components/LoginDialog'

const { proxy } = getCurrentInstance()

// 登录事件
const loginVisible = ref(false)
proxy.$emitter.on('showLoginDialog', () => {
  loginVisible.value = true
})
const handleLoginSuccess = () => {}
</script>

<style lang="scss" scoped>
</style>
