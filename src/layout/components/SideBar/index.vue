<template>
  <div class="h100% flex flex-[0] flex-col border-r-#DCDFE6 border-r-solid border-r-1px items-center side-bar">
    <!-- logo -->
    <div class="w-64 h-64 p-4 flex items-center justify-center">
      <img class="block w-40 h-40" :src="settingStore.logoSrc">
    </div>
    <!-- 菜单 -->
    <div class="flex flex-col py-8px px-4px">
      <div
        v-for="(item, index) in menusList"
        :key="index"
        class="w-40 h-40 f-c-c cursor-pointer mb-4 icon-item"
        @click="handleRoute(item.path)"
      >
        <div class="icon-box">
          <img class="w-20 h-20" :src="isActiveRoute(item.path) ? item.activeIcon : item.icon" alt="">
        </div>
        <div class="icon-text" :style="{ color: isActiveRoute(item.path) ? '#165DFF' : '#303133'}">{{ item.name }}</div>
      </div>
    </div>
    <!-- 头像 -->
    <div class="h48 f-c-c mt-auto">
      <HeaderBar />
    </div>
  </div>
</template>

<script setup name="SideBar">
import useSettingStore from '@/store/modules/settings'
import home from '@/assets/images/svg/icon_home1.svg'
import homeActive from '@/assets/images/svg/icon_home1_active.svg'
import history from '@/assets/images/svg/icon_history.svg'
import historyActive from '@/assets/images/svg/icon_history_active.svg'
import book from '@/assets/images/svg/icon_book.svg'
import bookActive from '@/assets/images/svg/icon_book_active.svg'
import setting from '@/assets/images/svg/icon_setting.svg'
import settingActive from '@/assets/images/svg/icon_setting_active.svg'
import Ai from '@/assets/images/svg/icon_Ai.svg'
import AiActive from '@/assets/images/svg/icon_Ai_active.svg'
import HeaderBar from '@/layout/components/HeaderBar'
import { getToken } from '@/utils/auth'
import { emitter } from '@/plugins'

const settingStore = useSettingStore()
const menusList = ref([
  {
    name: '对话',
    path: '/',
    icon: home,
    activeIcon: homeActive
  },
  {
    name: '智能体',
    path: '/agent',
    icon: Ai,
    activeIcon: AiActive
  },
  {
    name: '历史',
    path: '/history',
    icon: history,
    activeIcon: historyActive
  },
  {
    name: '知识库',
    path: '/knowledge',
    icon: book,
    activeIcon: bookActive
  },
  {
    name: '设置',
    path: '/setting',
    icon: setting,
    activeIcon: settingActive
  }
])
const router = useRouter()
const route = useRoute()

function isActiveRoute(menuPath) {
  if (menuPath === '/') {
    return route.path === '/' || route.path === '/chat'
  }
  const currentMainPath = '/' + route.path.split('/')[1]
  return currentMainPath === menuPath
}

function handleRoute(path) {
  if (!getToken()) {
    emitter.emit('showLoginDialog')
  } else {
    router.push(path)
  }
}
</script>

<style lang="scss" scoped>
// .isActive{
//   background: #E8F3FF;
//   border-radius: 4px;
// }
.side-bar{
  position: fixed;
  left: 0;
  top: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 9;
}
.icon-item{
  display: flex;
  flex-wrap: wrap;
  height: 60px;
  margin-bottom: 24px;
  .icon-box{
    width: 44px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .icon-text{
    color: #303133;
    font-weight: 400;
    font-size: 12px;
    height: 20px;
    text-align: center;
    line-height: 20px;
  }
}
</style>
