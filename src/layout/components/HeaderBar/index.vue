<template>
  <div class="header-bar">
    <div />
    <el-dropdown @command="handleCommand">
      <div class="avatar-container">
        <!-- <img class="avatar" :src="userStore.userInfo.avatar==''?avatar:userStore.userInfo.avatar" alt=""> -->
        <img class="avatar" :src="userStore.userInfo.avatar||avatar" alt="">
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-if="tokenB" command="setting">
            <el-icon><User /></el-icon>个人中心
          </el-dropdown-item>
          <el-dropdown-item v-if="tokenB" command="logout" divided>
            <el-icon><SwitchButton /></el-icon>退出登录
          </el-dropdown-item>
          <el-dropdown-item v-if="!tokenB" command="login" divided>
            <el-icon><User /></el-icon>请登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
  <CustomDialog
    v-if="showLoginDialog"
    :visible="showLoginDialog"
    @update:visible="showLoginDialog = $event"
    @login-success="handleLoginSuccess"
  />
</template>

<script setup name="HeaderBar">
import avatar from '@/assets/images/avatar2.png'
import { User, SwitchButton } from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'
import { useRouter } from 'vue-router'
import { ref, watch } from 'vue'
import { getToken, removeToken } from '@/utils/auth'
import CustomDialog from '@/components/LoginDialog/index.vue'
const userStore = useUserStore()
const router = useRouter()
const tokenB = ref(!!getToken())
const handleCommand = (command) => {
  if (command === 'setting') {
    // 跳转到个人中心页面
    router.push('/setting')
  } else if (command === 'logout') {
    // 调用退出登录接口
    userStore.logOut().then(() => {
      removeToken() // 移除本地存储的 token
      tokenB.value = getToken()
      router.push('/')
    })
  } else if (command === 'login') {
    // 显示登录弹窗
    showLoginDialog.value = true
  }
}
const showLoginDialog = ref(false)

function handleLoginSuccess(token) {
  console.log('handleLoginSuccess', token)
  showLoginDialog.value = false
  tokenB.value = getToken()
  // 处理登录成功后的逻辑
}

// 监听 userStore 中的 token 变化
watch(
  () => userStore.token,
  (newToken) => {
    console.log('Token变了:', newToken)
  }
)
</script>

<style lang="scss" scoped>
.header-bar {
  width: 40px;
  height: 40px;
  // padding: 0 16px;
  // border-bottom: 1px solid #E5E6EB;
  // background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.avatar-container {
  cursor: pointer;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  .el-icon {
    margin-right: 8px;
  }
}

:deep(.el-dropdown-menu) {
  padding: 4px 0;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  font-size: 14px;
  line-height: 1.5;

  &:hover {
    background-color: #f5f7fa;
  }
}
</style>
