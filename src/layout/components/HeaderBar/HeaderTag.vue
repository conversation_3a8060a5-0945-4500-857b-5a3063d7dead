<template>
  <div
    :class="[
      'header-tag border-1px border-solid bg-#F7F8FA px-12 py-3 border-rd-4px text-12',
      [active ? 'border-#4E5969 text-#1D2129' : 'border-#F7F8FA text-#4E5969'],
    ]"
  >
    <slot />
  </div>
</template>

<script setup name="HeaderTag">
const props = defineProps({
  active: {
    type: Boolean,
    default: false
  }
})
console.log(`output->props`, props)
</script>

<style lang="scss" scoped>
.header-tag + .header-tag {
  margin-left: 20px;
}
</style>
