<template>
  <div class="knowledge-list" :style="{ backgroundImage: `url(${bgImage})` }">
    <div class="content-wrapper">
      <!-- 顶部标题区域 -->
      <div class="header-section">
        <main-header title="知识库" info="一库千面・智答随心" />

        <!-- 筛选和搜索区域 -->
        <div class="filter-section">
          <div class="filter-tabs">
            <el-radio-group v-model="currentFilter" @change="handleFilterChange">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="team">团队知识库</el-radio-button>
              <el-radio-button label="personal">个人知识库</el-radio-button>
              <el-radio-button label="share" class="share-radio-button">
                共享知识库
                <el-badge v-if="pendingShareCount > 0" :value="pendingShareCount" class="share-badge" />
              </el-radio-button>
            </el-radio-group>
          </div>
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索知识库"
              clearable
              @input="handleSearch"
              @clear="clearSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </div>

      <!-- 知识库列表 - 可滚动区域 -->
      <div class="scrollable-area">
        <div class="cards-container">
          <!-- 创建知识库卡片 - 不在共享知识库tab中显示 -->
          <div v-if="currentFilter !== 'share'" class="knowledge-card create-card" @click="handleAdd">
            <div class="card-content">
              <div class="create-icon">
                <el-icon><Plus /></el-icon>
              </div>
              <span class="create-text">创建知识库</span>
            </div>
          </div>

          <!-- 空状态提示 - 当共享知识库为空时显示 -->
          <div v-if="currentFilter === 'share' && filteredList.length === 0" class="empty-shared-wrapper">
            <div class="empty-shared-container">
              <el-empty
                description="暂无共享知识库"
                :image-size="120"
              >
                <template #description>
                  <p>暂无共享知识库</p>
                  <span class="empty-text">您还没有接收到任何共享给您的知识库</span>
                </template>
              </el-empty>
            </div>
          </div>

          <!-- 知识库列表 -->
          <div
            v-for="item in filteredList"
            :key="item.id"
            class="knowledge-card"
            @click="handleLibraryClick(item)"
          >
            <div class="card-content">
              <!-- 头部：文件夹图标和更多操作 -->
              <div class="card-header">
                <!-- 根据知识库类型显示不同的图标 -->
                <template v-if="item.permission === 'share'">
                  <img
                    src="@/assets/images/svg/share-file.svg"
                    alt="share file"
                    class="folder-icon"
                  >
                </template>
                <template v-else>
                  <img
                    :src="getKnowledgeIcon(item)"
                    alt=""
                    class="folder-icon"
                  >
                </template>
                <el-dropdown trigger="click" @command="handleCommand($event, item)">
                  <img
                    :src="moreIcon"
                    alt=""
                    class="more-icon"
                    @click.stop
                  >
                  <template #dropdown>
                    <el-dropdown-menu>
                      <!-- 个人知识库操作：编辑、共享、删除 -->
                      <template v-if="item.permission === 'me'">
                        <el-dropdown-item
                          command="edit"
                          :disabled="!hasEditPermission(item)"
                          @click.stop
                        >
                          <div class="dropdown-item">
                            <img src="@/assets/images/svg/knowledge-edit.svg" style="padding-right:8px;" alt="edit">
                            <span
                              :style="{
                                color: !hasEditPermission(item) ? '#909399' : '#303133',
                                fontSize: '16px',
                                fontWeight: '500'
                              }"
                              class="delete"
                            >编辑</span>
                          </div>
                        </el-dropdown-item>
                        <el-dropdown-item
                          command="share"
                          @click.stop
                        >
                          <div class="dropdown-item">
                            <img src="@/assets/images/svg/knowledge-share.svg" style="padding-right:8px;" alt="share">
                            <span
                              style="color: #303133; fontSize: 16px; fontWeight: 500;"
                              class="share"
                            >共享</span>
                          </div>
                        </el-dropdown-item>
                        <el-dropdown-item
                          command="delete"
                          :disabled="!hasEditPermission(item)"
                          @click.stop
                        >
                          <div class="dropdown-item">
                            <img src="@/assets/images/svg/knowledge-trash.svg" style="padding-right:8px" alt="delete">
                            <span
                              :style="{
                                color: !hasEditPermission(item) ? '#909399' : '#F56C6C',
                                fontSize: '16px',
                                fontWeight: '500'
                              }"
                              class="delete"
                            >删除</span>
                          </div>
                        </el-dropdown-item>
                      </template>

                      <!-- 共享知识库操作：根据shareHandleStatus展示不同操作 -->
                      <template v-else-if="item.permission === 'share' || (item.permission === 'me' && item.shareHandleStatus)">
                        <!-- 待处理状态：显示同意/拒绝按钮 -->
                        <template v-if="item.shareHandleStatus === 'wait'">
                          <el-dropdown-item
                            command="accept"
                            @click.stop
                          >
                            <div class="dropdown-item">
                              <el-icon style="padding-right:8px; color: #67C23A;"><Check /></el-icon>
                              <span
                                style="color: #67C23A; fontSize: 16px; fontWeight: 500;"
                                class="accept"
                              >同意加入</span>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item
                            command="reject"
                            @click.stop
                          >
                            <div class="dropdown-item">
                              <el-icon style="padding-right:8px; color: #F56C6C;"><Close /></el-icon>
                              <span
                                style="color: #F56C6C; fontSize: 16px; fontWeight: 500;"
                                class="reject"
                              >拒绝加入</span>
                            </div>
                          </el-dropdown-item>
                        </template>

                        <!-- 已加入状态：显示退出按钮 -->
                        <template v-else-if="item.shareHandleStatus === 'agree'">
                          <el-dropdown-item
                            command="exit"
                            @click.stop
                          >
                            <div class="dropdown-item">
                              <img src="@/assets/images/svg/cancel.svg" style="padding-right:8px;height:24px;width:24px;" alt="exit">
                              <span
                                style="color: #1296DB; fontSize: 16px; fontWeight: 500;"
                                class="exit"
                              >退出知识库</span>
                            </div>
                          </el-dropdown-item>
                        </template>
                      </template>

                      <!-- 团队知识库操作 -->
                      <template v-else>
                        <el-dropdown-item
                          command="edit"
                          :disabled="!item.belongMe"
                          @click.stop
                        >
                          <div class="dropdown-item">
                            <img src="@/assets/images/svg/knowledge-edit.svg" style="padding-right:8px;" alt="edit">
                            <span
                              :style="{
                                color: !item.belongMe ? '#909399' : '#303133',
                                fontSize: '16px',
                                fontWeight: '500'
                              }"
                              class="delete"
                            >编辑</span>
                          </div>
                        </el-dropdown-item>
                        <!-- 根据belongDept字段判断是否可分享 -->
                        <el-dropdown-item
                          v-if="item.belongDept"
                          command="share"
                          :disabled="!item.belongMe"
                          @click.stop
                        >
                          <div class="dropdown-item">
                            <img src="@/assets/images/svg/knowledge-share.svg" style="padding-right:8px;" alt="share">
                            <span
                              :style="{
                                color: !item.belongMe ? '#909399' : '#303133',
                                fontSize: '16px',
                                fontWeight: '500'
                              }"
                              class="share"
                            >共享</span>
                          </div>
                        </el-dropdown-item>
                        <el-dropdown-item
                          command="delete"
                          :disabled="!item.belongMe"
                          @click.stop
                        >
                          <div class="dropdown-item">
                            <img src="@/assets/images/svg/knowledge-trash.svg" style="padding-right:8px" alt="delete">
                            <span
                              :style="{
                                color: !item.belongMe ? '#909399' : '#F56C6C',
                                fontSize: '16px',
                                fontWeight: '500'
                              }"
                              class="delete"
                            >删除</span>
                          </div>
                        </el-dropdown-item>
                      </template>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>

              <!-- 标题 -->
              <div class="kb-title">{{ item.name }}
              </div>
              <!-- 描述 -->
              <div class="kb-desc">{{ item.description || '暂无描述' }}</div>

              <!-- 底部：类型和标签 -->
              <div class="card-footer">
                <span class="kb-type" :class="{ 'personal': item.permission === 'me', 'team': item.permission === 'team', 'share': item.permission === 'share' }">
                  {{ getKnowledgeTypeLabel(item) }}
                </span>
                <span v-if="item.permission === 'me' && item.shareStatus" class="kb-status shared-tag">已共享</span>
                <span v-if="item.permission === 'team' && item.shareStatus && item.belongDept && item.belongMe" class="kb-status shared-tag">已共享</span>
                <span v-if="item.permission === 'team' && item.shareStatus && item.belongDept && !item.belongMe" class="kb-status shared-tag">共享</span>
                <span v-if="item.permission === 'share' && item.shareHandleStatus === 'wait'" class="kb-status pending">待接收</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑知识库弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="form.id ? '编辑知识库' : '创建知识库'"
      width="640px"
      style="margin-top: 20vh"
      custom-class="create-dialog"
      :before-close="handleClose"
    >
      <div style="width:640px;height:1px;margin-left:-16px;margin-bottom:12px; background-color:#E5E6EB" />
      <div class="dialog-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="100px"
        >
          <el-form-item label="知识库类型">
            <el-radio-group v-model="form.permission" :disabled="!!form.id">
              <el-radio label="me">个人知识库</el-radio>
              <el-radio v-if="isAdmin || roleName === 'DAdmin'" label="team">团队知识库</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="知识库名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入知识库名称"
              maxlength="30"
              show-word-limit
              class="custom-input"
            />
          </el-form-item>

          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 5 }"
              placeholder="为知识库添加描述..."
              maxlength="200"
              show-word-limit
              class="custom-input"
            />
          </el-form-item>

          <!-- <el-form-item label="启用状态">
            <el-switch v-model="form.status" />
          </el-form-item> -->
        </el-form>
      </div>
      <div style="width:640px;height:1px;margin-left:-16px;background-color:#E5E6EB" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 共享知识库弹窗 -->
    <ShareDialog
      v-if="shareDialogVisible"
      v-model:visible="shareDialogVisible"
      :knowledge-base-id="currentKnowledgeBase?.id"
      :knowledge-base-name="currentKnowledgeBase?.name"
      :knowledge-base-type="currentKnowledgeBase?.permission"
      @share-success="handleShareSuccess"
    />

    <!-- 已分享人员列表弹窗 -->
    <el-dialog
      v-model="sharedMembersVisible"
      title="知识库成员"
      width="800px"
      style="margin-top: 25vh;"
      destroy-on-close
    >
      <div class="dialog-content">
        <!-- 成员列表表格 -->
        <div style="width:800px;height:1px;margin-left:-16px;margin-bottom:16px;background-color:#E5E6EB" />

        <el-table
          ref="memberTableRef"
          v-loading="membersLoading"
          :data="filteredMembers"
          style="width: 100%; border: 1px solid #EBEEF5; border-radius: 8px;"
          :header-cell-style="{background:'#F5F7FA'}"
          :cell-style="{padding:'8px 0'}"
          row-key="tenantId"
          @selection-change="handleMemberSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="() => true"
            :reserve-selection="true"
          />
          <el-table-column label="部门" prop="fullDeptName" />
          <el-table-column label="姓名" prop="tenantName" />
          <el-table-column label="手机号" prop="phonenumber" />
          <el-table-column label="共享时间" prop="shareAcceptTime" width="180" />
          <el-table-column label="操作" width="100" align="center">
            <template #default="scope">
              <el-button
                type="text"
                @click="handleRemoveMember(scope.row)"
              >
                <el-icon color="#F56C6C"><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="width:800px;height:1px;margin-left:-16px;margin-top:20px;background-color:#E5E6EB" />
      </div>

      <!-- 添加底部操作区 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button
            :disabled="selectedMembers.length === 0"
            @click="handleBatchRemoveMembers"
          >
            批量删除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 已分享部门列表弹窗 -->
    <el-dialog
      v-model="sharedDeptsVisible"
      title="已分享部门"
      width="800px"
      style="margin-top: 25vh;"
      destroy-on-close
    >
      <div class="dialog-content">
        <!-- 部门列表表格 -->
        <div style="width:800px;height:1px;margin-left:-16px;margin-bottom:16px;background-color:#E5E6EB" />

        <el-table
          ref="deptTableRef"
          v-loading="deptsLoading"
          :data="filteredDepts"
          style="width: 100%; border: 1px solid #EBEEF5; border-radius: 8px;"
          :header-cell-style="{background:'#F5F7FA'}"
          :cell-style="{padding:'8px 0'}"
          row-key="deptId"
          @selection-change="handleDeptSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="() => true"
            :reserve-selection="true"
          />
          <el-table-column label="部门名称" prop="deptName" />
          <el-table-column label="部门负责人" prop="leader" />
          <el-table-column label="共享时间" prop="shareTime" width="180" />
          <el-table-column label="操作" width="100" align="center">
            <template #default="scope">
              <el-button
                type="text"
                @click="handleRemoveDept(scope.row)"
              >
                <el-icon color="#F56C6C"><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="width:800px;height:1px;margin-left:-16px;margin-top:20px;background-color:#E5E6EB" />
      </div>

      <!-- 添加底部操作区 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button
            :disabled="selectedDepts.length === 0"
            @click="handleBatchRemoveDepts"
          >
            批量删除
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import MainHeader from '@/components/MainHeader'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Search, Check, Close, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import useUserStore from '@/store/modules/user'
import bgImage from '@/assets/images/knowledge-bg.png'
import personalFileIcon from '@/assets/images/svg/personal-file.svg'
import teamFileIcon from '@/assets/images/svg/team-file.svg'
import shareFileIcon from '@/assets/images/svg/share-file.svg'
import moreIcon from '@/assets/images/svg/icon-more.svg'
import ShareDialog from '@/components/ShareDialog.vue'

const router = useRouter()
const dialogVisible = ref(false)
const shareDialogVisible = ref(false)
const currentKnowledgeBase = ref(null)
const formRef = ref(null)
const searchQuery = ref('')
const knowledgeList = ref([])
const knowledgeListShare = ref([])
const knowledgeListShared = ref([]) // 共享知识库
const currentFilter = ref('all')

// 添加已分享人员/部门相关变量
const sharedMembersVisible = ref(false)
const sharedDeptsVisible = ref(false)
const membersLoading = ref(false)
const deptsLoading = ref(false)
const sharedMembers = ref([])
const sharedDepts = ref([])
const selectedMembers = ref([])
const selectedDepts = ref([])
const memberSearchQuery = ref('')
const deptSearchQuery = ref('')

const roleName = useUserStore().roles[0]
const rules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

// 在 script setup 中添加
const isAdmin = computed(() => {
  return roleName === 'admin' || roleName === 'TAdmin'
})

// 修改权限判断函数，添加普通用户对个人知识库的权限
const hasEditPermission = (item) => {
  // 超级管理员和团队管理员有全部权限
  if (roleName === 'admin' || roleName === 'TAdmin') return true

  // DAdmin可以编辑个人知识库和belongDept为true的团队知识库
  if (roleName === 'DAdmin') {
    if (item.permission === 'me') return true
    if (item.permission === 'team' && item.belongDept) return true
    return false
  }

  // 普通用户可以编辑自己的个人知识库
  if (roleName === 'common' && item.permission === 'me') return true

  return false
}

// 修改表单的初始值
const form = ref({
  id: '',
  name: '',
  description: '',
  permission: 'me' // 默认为个人知识库
})

const filteredList = computed(() => {
  let list = []

  if (currentFilter.value === 'all') {
    // 将团队知识库放在前面，然后是共享知识库，最后是个人知识库
    list = [...knowledgeListShare.value, ...knowledgeListShared.value, ...knowledgeList.value]
  } else if (currentFilter.value === 'team') {
    list = knowledgeListShare.value
  } else if (currentFilter.value === 'personal') {
    list = knowledgeList.value
  } else if (currentFilter.value === 'share') {
    // 共享知识库包括：1) permission为share的知识库 2) permission为me但有shareHandleStatus属性的知识库
    list = knowledgeListShared.value.filter(item =>
      item.permission === 'share' || (item.permission === 'me' && item.shareHandleStatus)
    )
  }

  // 确保每个项目都有必要的字段，避免渲染错误
  list = list.map(item => ({
    ...item,
    // 确保必要的字段存在
    id: item.id || '',
    name: item.name || '未命名知识库',
    description: item.description || '',
    permission: item.permission || 'me',
    isShared: item.isShared || false,
    shareHandleStatus: item.shareHandleStatus || ''
  }))

  return list.filter(item =>
    item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const handleSearch = () => {
  // 使用计算属性自动过滤
}

const clearSearch = () => {
  searchQuery.value = ''
}

const handleFilterChange = (value) => {
  currentFilter.value = value

  // 根据选择的标签加载对应的知识库数据
  if (value === 'all') {
    // 全部知识库，确保三种类型的知识库都加载
    getKnowledgeList()
  } else if (value === 'team') {
    // 团队知识库
    getTeamKnowledgeList()
  } else if (value === 'personal') {
    // 个人知识库
    getPersonalKnowledgeList()
  } else if (value === 'share') {
    // 共享知识库
    getSharedKnowledgeList()
  }
}

const getKnowledgeList = async() => {
  // 根据当前筛选条件决定加载哪些数据
  await Promise.all([
    getTeamKnowledgeList(),
    getPersonalKnowledgeList(),
    getSharedKnowledgeList()
  ])
}

// 获取团队知识库
const getTeamKnowledgeList = async() => {
  // 如果不是全部或团队知识库页签，且已有数据，则不重新获取
  if (currentFilter.value !== 'all' && currentFilter.value !== 'team' && knowledgeListShare.value.length > 0) {
    return
  }

  try {
    const teamRes = await request({
      url: '/cms/knowledgebase/list',
      method: 'get',
      params: { permission: 'team' }
    })

    // 注意接口直接返回数组 []
    if (Array.isArray(teamRes)) {
      // 处理返回的团队知识库数据
      knowledgeListShare.value = teamRes.map(item => ({
        ...item,
        permission: 'team',
        // 确保每个知识库项都有belongDept和belongMe字段
        belongDept: item.belongDept || false,
        belongMe: item.belongMe || false
      }))
    } else if (teamRes.code === 0 && Array.isArray(teamRes.data)) {
      // 兼容可能的格式变化，如果返回 {code:0, data:[]} 格式
      knowledgeListShare.value = teamRes.data.map(item => ({
        ...item,
        permission: 'team',
        belongDept: item.belongDept || false,
        belongMe: item.belongMe || false
      }))
    } else {
      console.error('获取团队知识库返回格式错误:', teamRes)
      knowledgeListShare.value = []
    }
  } catch (error) {
    console.error('获取团队知识库列表失败:', error)
    knowledgeListShare.value = []
  }
}

// 获取个人知识库
const getPersonalKnowledgeList = async() => {
  // 如果不是全部或个人知识库页签，且已有数据，则不重新获取
  if (currentFilter.value !== 'all' && currentFilter.value !== 'personal' && knowledgeList.value.length > 0) {
    return
  }

  try {
    const personalRes = await request({
      url: '/cms/knowledgebase/list',
      method: 'get',
      params: { permission: 'me' }
    })

    // 注意接口直接返回数组 []
    if (Array.isArray(personalRes)) {
      // 确保每个知识库项都有isShared字段
      knowledgeList.value = personalRes.map(item => ({
        ...item,
        isShared: item.isShared || false // 确保有isShared字段，默认为false
      }))
    } else if (personalRes.code === 0 && Array.isArray(personalRes.data)) {
      // 兼容可能的格式变化，如果返回 {code:0, data:[]} 格式
      knowledgeList.value = personalRes.data.map(item => ({
        ...item,
        isShared: item.isShared || false
      }))
    } else {
      console.error('获取个人知识库返回格式错误:', personalRes)
      knowledgeList.value = []
    }
  } catch (error) {
    console.error('获取个人知识库列表失败:', error)
    knowledgeList.value = [
      {
        'avatar': null,
        'chunk_count': 0,
        'chunk_method': 'naive',
        'create_date': 'Fri, 28 Mar 2025 16:44:38 GMT',
        'create_time': 1743151478110,
        'created_by': 'dc7f1628f9ca11efaa4d0242ac120004',
        'description': '个人研究项目文档',
        'document_count': 0,
        'embedding_model': 'BAAI/bge-large-zh-v1.5@BAAI',
        'id': 'e1eddaa20bb011f099910242ac120003',
        'language': 'Chinese',
        'name': '研究项目',
        'pagerank': 0,
        'parser_config': {
          'chunk_token_num': 128,
          'delimiter': '\\n!?;。；！？',
          'html4excel': false,
          'layout_recognize': 'DeepDOC',
          'raptor': {
            'use_raptor': false
          }
        },
        'permission': 'me',
        'isShared': true, // 已共享状态
        'similarity_threshold': 0.2,
        'status': '1',
        'tenant_id': 'dc7f1628f9ca11efaa4d0242ac120004',
        'token_num': 0,
        'update_date': 'Wed, 23 Apr 2025 13:59:38 GMT',
        'update_time': 1745387978757,
        'vector_similarity_weight': 0.3
      },
      {
        'avatar': null,
        'chunk_count': 0,
        'chunk_method': 'naive',
        'create_date': 'Wed, 23 Apr 2025 10:28:22 GMT',
        'create_time': 1743151478110,
        'created_by': 'dc7f1628f9ca11efaa4d0242ac120004',
        'description': '个人工作笔记',
        'document_count': 0,
        'embedding_model': 'BAAI/bge-large-zh-v1.5@BAAI',
        'id': 'e1eddaa20bb011f099910242ac120004',
        'language': 'Chinese',
        'name': '工作笔记',
        'pagerank': 0,
        'parser_config': {
          'chunk_token_num': 128,
          'delimiter': '\\n!?;。；！？',
          'html4excel': false,
          'layout_recognize': 'DeepDOC',
          'raptor': {
            'use_raptor': false
          }
        },
        'permission': 'me',
        'isShared': false, // 未共享状态
        'similarity_threshold': 0.2,
        'status': '1',
        'tenant_id': 'dc7f1628f9ca11efaa4d0242ac120004',
        'token_num': 0,
        'update_date': 'Wed, 23 Apr 2025 13:59:38 GMT',
        'update_time': 1745387978757,
        'vector_similarity_weight': 0.3
      }
    ]
  }
}

// 获取共享知识库
const getSharedKnowledgeList = async() => {
  // 不管当前是什么页签，都获取共享知识库列表以计算徽标数量
  // 如果是全部或共享知识库页签，或者共享知识库列表为空，则获取数据
  if (currentFilter.value === 'all' || currentFilter.value === 'share' || knowledgeListShared.value.length === 0) {
    try {
      const sharedRes = await request({
        url: '/cms/knowledgebase/list',
        method: 'get',
        params: { permission: 'share' }
      })

      // 注意接口直接返回数组 []
      if (Array.isArray(sharedRes)) {
        // 处理返回的知识库数据
        knowledgeListShared.value = sharedRes.map(item => {
          // 确保每个项都有必要的属性
          const processedItem = {
            ...item,
            // permission可能是me或share，保持原值
            permission: item.permission || 'share',
            // 如果没有shareHandleStatus，设为默认值
            shareHandleStatus: item.shareHandleStatus || ''
          }
          return processedItem
        })
      } else if (sharedRes.code === 0 && Array.isArray(sharedRes.data)) {
        // 兼容可能的格式变化，如果返回 {code:0, data:[]} 格式
        knowledgeListShared.value = sharedRes.data.map(item => {
          const processedItem = {
            ...item,
            permission: item.permission || 'share',
            shareHandleStatus: item.shareHandleStatus || ''
          }
          return processedItem
        })
      } else {
        console.error('获取共享知识库返回格式错误:', sharedRes)
        knowledgeListShared.value = []
      }
    } catch (error) {
      console.error('获取共享知识库列表失败:', error)
      // 保留原有的示例数据
      knowledgeListShared.value = [
        {
          'avatar': null,
          'chunk_count': 15,
          'chunk_method': 'naive',
          'create_date': 'Fri, 28 Mar 2025 16:44:38 GMT',
          'create_time': 1743151478110,
          'created_by': 'dc7f1628f9ca11efaa4d0242ac120004',
          'description': '市场部共享的营销资料库',
          'document_count': 5,
          'embedding_model': 'BAAI/bge-large-zh-v1.5@BAAI',
          'id': 'e1eddaa20bb011f099910242ac120001',
          'language': 'Chinese',
          'name': '营销资料共享',
          'pagerank': 0,
          'parser_config': {
            'chunk_token_num': 128,
            'delimiter': '\\n!?;。；！？',
            'html4excel': false,
            'layout_recognize': 'DeepDOC',
            'raptor': {
              'use_raptor': false
            }
          },
          'permission': 'share',
          'shareHandleStatus': 'wait', // 待接收
          'similarity_threshold': 0.2,
          'status': '1',
          'tenant_id': 'dc7f1628f9ca11efaa4d0242ac120004',
          'token_num': 0,
          'update_date': 'Wed, 23 Apr 2025 13:59:38 GMT',
          'update_time': 1745387978757,
          'vector_similarity_weight': 0.3
        },
        {
          'avatar': null,
          'chunk_count': 32,
          'chunk_method': 'naive',
          'create_date': 'Mon, 15 Apr 2025 10:22:15 GMT',
          'create_time': 1743151478110,
          'created_by': 'dc7f1628f9ca11efaa4d0242ac120005',
          'description': '法务部共享的合同模板库',
          'document_count': 12,
          'embedding_model': 'BAAI/bge-large-zh-v1.5@BAAI',
          'id': 'e1eddaa20bb011f099910242ac120002',
          'language': 'Chinese',
          'name': '合同模板库',
          'pagerank': 0,
          'parser_config': {
            'chunk_token_num': 128,
            'delimiter': '\\n!?;。；！？',
            'html4excel': false,
            'layout_recognize': 'DeepDOC',
            'raptor': {
              'use_raptor': false
            }
          },
          'permission': 'share',
          'shareHandleStatus': 'agree', // 已加入
          'similarity_threshold': 0.2,
          'status': '1',
          'tenant_id': 'dc7f1628f9ca11efaa4d0242ac120005',
          'token_num': 0,
          'update_date': 'Wed, 25 Apr 2025 09:18:22 GMT',
          'update_time': 1745387978757,
          'vector_similarity_weight': 0.3
        }
      ]
    }
  }
}

const handleLibraryClick = (item) => {
  router.push({
    name: 'KnowledgeDetail',
    query: { id: item.id, permission: item.permission } // 添加 permission 参数
  })
}

// 获取知识库图标
const getKnowledgeIcon = (item) => {
  if (item.permission === 'me') {
    return personalFileIcon
  } else if (item.permission === 'team') {
    return teamFileIcon
  } else {
    return shareFileIcon
  }
}

// 获取知识库类型标签
const getKnowledgeTypeLabel = (item) => {
  if (item.permission === 'share') {
    return '共享知识库'
  } else if (item.permission === 'me') {
    return '个人知识库'
  } else {
    return '团队知识库'
  }
}

const handleCommand = (command, item) => {
  // 权限判断 - 针对编辑和删除操作
  if ((command === 'edit' || command === 'delete') && !hasEditPermission(item)) {
    ElMessage.warning('您没有权限操作该知识库')
    return
  }

  if (command === 'edit') {
    form.value = { ...item }
    dialogVisible.value = true
  } else if (command === 'delete') {
    handleDeleteDataset(item)
  } else if (command === 'share') {
    openShareDialog(item)
  } else if (command === 'accept') {
    acceptShare(item)
  } else if (command === 'reject') {
    rejectShare(item)
  } else if (command === 'exit') {
    exitShare(item)
  }
}

async function handleDeleteDataset(item) {
  try {
    await ElMessageBox.confirm(
      '确定要删除该知识库吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // 这里添加删除文件的接口调用
    const res = await request({
      url: `/knowledge/api/v1/datasets`,
      method: 'delete',
      data: { ids: [item.id] }
    })
    if (res.code === 0) {
      ElMessage.success(`删除知识库${item.name}成功`)
      getKnowledgeList()
    }
  } catch {
    // 用户取消删除
  }
}

const handleAdd = () => {
  // 检查用户是否登录
  if (!useUserStore().token) {
    ElMessage.warning('请先登录')
    return
  }

  form.value = {
    id: '',
    name: '',
    description: '',
    permission: 'me'
  }
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
  form.value = {
    id: '',
    name: '',
    description: '',
    permission: 'me' // 添加默认值
  }
}

const handleSubmit = () => {
  if (!formRef.value) return
  formRef.value.validate(async(valid) => {
    if (valid) {
      try {
        const url = form.value.id ? '/knowledge/api/v1/datasets/' + form.value.id : '/knowledge/api/v1/datasets'
        const method = form.value.id ? 'put' : 'post'
        const res = await request({
          url,
          method,
          data: { name: form.value.name, description: form.value.description, permission: form.value.permission },
          headers: {
            'zs2': useUserStore().userInfo?.zs2 || ''
          }
        })
        if (res.code === 0) {
          ElMessage.success(form.value.id ? '编辑成功' : '创建成功')
          useUserStore().getKnowlegeLib()
          handleClose()
          getKnowledgeList()
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    }
  })
}

// 打开共享弹窗
const openShareDialog = (item) => {
  currentKnowledgeBase.value = item
  // 确保设置当前知识库ID后再显示弹窗
  if (item && item.id) {
    shareDialogVisible.value = true
  } else {
    ElMessage.error('知识库ID不存在，无法分享')
  }
}

// 共享成功后回调
const handleShareSuccess = () => {
  getKnowledgeList()
  getSharedKnowledgeList()
}

// 同意加入共享知识库
const acceptShare = async(item) => {
  try {
    const res = await request({
      url: '/cms/knowledgebase/handleShare',
      method: 'post',
      data: {
        knowledgeId: item.id,
        status: 'agree'
      }
    })
    if (res.code === 0 || res.code === 200) {
      ElMessage.success('已同意加入知识库')
      // 更新当前知识库的状态为已加入
      item.shareHandleStatus = 'agree'
      getSharedKnowledgeList() // 刷新共享知识库列表
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 拒绝加入共享知识库
const rejectShare = async(item) => {
  try {
    const res = await request({
      url: '/cms/knowledgebase/handleShare',
      method: 'post',
      data: {
        knowledgeId: item.id,
        status: 'refuse'
      }
    })
    if (res.code === 0 || res.code === 200) {
      ElMessage.success('已拒绝加入知识库')
      // 从列表中移除该知识库
      knowledgeListShared.value = knowledgeListShared.value.filter(kb => kb.id !== item.id)
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 退出共享知识库
const exitShare = async(item) => {
  try {
    await ElMessageBox.confirm(
      '确定要退出该知识库吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    const res = await request({
      url: '/cms/knowledgebase/handleShare',
      method: 'post',
      data: {
        knowledgeId: item.id,
        status: 'quit'
      }
    })
    if (res.code === 0 || res.code === 200) {
      ElMessage.success('已成功退出知识库')
      // 从列表中移除该知识库
      knowledgeListShared.value = knowledgeListShared.value.filter(kb => kb.id !== item.id)
    }
  } catch (error) {
    if (error) {
      console.error('退出知识库失败:', error)
      ElMessage.error('操作失败')
    }
    // 用户取消操作，不做处理
  }
}

// 计算待确认的共享知识库数量
const pendingShareCount = computed(() => {
  // 计算待接收的共享知识库数量
  return knowledgeListShared.value.filter(item =>
    (item.permission === 'share' || (item.permission === 'me' && item.shareHandleStatus)) &&
    item.shareHandleStatus === 'wait'
  ).length
})

// 初始化加载知识库列表
getKnowledgeList()

// Debug: 检查图标路径
console.log('图标路径:', {
  personal: personalFileIcon,
  team: teamFileIcon,
  share: shareFileIcon
})
</script>

<style scoped lang="scss">
.knowledge-list {
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  padding: 24px;
}

.content-wrapper {
  max-width: 900px;
  margin: 0 auto;
  height: calc(100vh - 48px);
  display: flex;
  flex-direction: column;
}

.header-section {
  flex-shrink: 0; /* 防止头部区域被压缩 */
}

.scrollable-area {
  padding-top: 4px;
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 允许垂直滚动 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24px auto;

  .filter-tabs {
    :deep(.el-radio-group) {
      display: flex;
      gap: 8px;
    }

    :deep(.el-radio-button__inner) {
      background: transparent;
      border: none;
      color: #6b7280;
      height: 40px;
      padding: 5px 15px;
      border-radius: 100px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #165DFF;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      background: #fff;
      color: #165DFF;
      box-shadow: none;
      font-weight:600;
    }

    // 移除相邻按钮的边框样式
    :deep(.el-radio-button:not(:first-child) .el-radio-button__inner) {
      border-left: none;
    }
  }
}

.search-box {
  width: 300px;
  border-radius:8px;

  :deep(.el-input__wrapper) {
    border-radius:8px;
    background: rgba(255, 255, 255, 0.9);
  }
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  padding-bottom: 24px; /* 底部留出一些空间 */
}

.empty-shared-wrapper {
  grid-column: 1 / -1; /* 横跨所有列 */
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  width: 100%;
}

.empty-shared-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  text-align: center;
  max-width: 400px;

  :deep(.el-empty__image) {
    height: 120px;
  }

  :deep(.el-empty__description) {
    margin-top: 20px;

    p {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
    }
  }
}

.empty-text {
  font-size: 14px;
  color: #909399;
  display: block;
  margin-top: 4px;
}

.knowledge-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  height:166px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.create-card {
  border: 2px dashed #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 166px;

  &:hover {
    border-color: #165DFF;

    .create-icon {
      color: #165DFF;
    }

    .create-text {
      color: #165DFF;
    }
  }

  .create-icon {
    color: #165DFF;
    font-size: 32px;
    margin-bottom: 8px;
  }

  .create-text {
    font-size: 16px;
    color: #165DFF;
  }

  .card-content {
    text-align: center;
  }
}

.card-content {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;

  .folder-icon {
    width: 48px;
    height: 48px;
  }

  .more-icon {
    width: 40px;
    height: 40px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

.kb-title {
  overflow: hidden;
  color: var(--el-text-color-primary);
  text-overflow: ellipsis;
  /* bold/Headline1-Bold */
  font-family: var(--el-font-family);
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: var(--el-font-line-height-extra-large);
}

.kb-desc {
  overflow: hidden;
  color: #606266;
  text-overflow: ellipsis;
  margin-top: 4px;
  /* regular/body1-Regular */
  font-family: var(--el-font-family);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: var(--el-font-line-height-base);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1; /* 显示1行 */
}

.card-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 8px;
  gap: 8px;

  .kb-type {
    font-size: 12px;
    line-height: 14px;
    padding: 4px 8px;
    border-radius: 4px;

    &.personal {
      background: #E8F3FF;
      color: #165DFF;
    }

    &.team {
      background: #FFF7E8;
      color: #FF7D00;
    }

    &.share {
      background: #F5E8FF;
      color: #722ED1;
    }
  }

  .kb-status {
    font-size: 12px;
    line-height: 14px;
    padding: 4px 8px;
    border-radius: 4px;

    &.shared {
      background: #E8F3FF;
      color: #165DFF;
    }

    &.shared-tag {
      background: #F5E8FF;
      color: #722ED1;
    }

    &.pending {
      background: #FFF7E8;
      color: #FF7D00;
    }
  }
}
.dropdown-item{
  display:flex;
  align-items: center;
}
:deep(.el-dropdown-menu) {
  padding: 4px 0;

  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 14px;

    &:hover {
      background-color: #f3f4f6;
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .edit {
        color: #303133;
      }

      .delete {
        color: #F56C6C;
      }
    }
  }
}

:deep(.el-dialog__title){
  font-weight: 550;
}

:deep(.el-form-item__label){
  font-weight: 400;
}

:deep(.create-dialog) {
  .el-dialog {
    margin: 0 !important;
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
  }

  .el-dialog__header {
    margin: 0;
    padding: 16px 24px;
    border-bottom: 1px solid red !important;
    background-color: #fff;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #1D2129;
      line-height: 24px;
    }

    .el-dialog__close {
      font-size: 16px;
      color: #86909C;
    }
  }

  .el-dialog__body {
    padding: 24px;
    margin: 0;
  }

  .el-dialog__footer {
    margin: 0;
    padding: 16px 24px;
    border-top: 1px solid #E5E6EB;
    background-color: #fff;
  }

  .custom-input {
    .el-input__wrapper,
    .el-textarea__inner {
      background-color: #F7F7F7;
      box-shadow: none !important;
    }

    .el-textarea__inner {
      min-height: 122px !important;
      resize: none;
    }
  }
}

.share-radio-button {
  position: relative;
}

.share-badge {
  position: absolute;
  top: -2px;
  right: -4px;
}

:deep(.share-badge .el-badge__content) {
  background-color: #F56C6C;
  border: none;
}
</style>
