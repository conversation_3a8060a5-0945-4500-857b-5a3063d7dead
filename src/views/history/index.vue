<script setup name="History">
import ChatPage from '@/components/ChatPage'
import dots from '@/assets/images/svg/icon_dots.svg'
import trash from '@/assets/images/svg/icon_trash.svg'
import request from '@/utils/request'
import useUserStore from '@/store/modules/user'
import { groupDataByTime } from '@/utils/lonely'
import { agentList } from '@/assets/dict'

const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
const currentId = ref('')
const chatPageRef = ref()

// 用户列表
const userList = ref([])
const selectedUserId = ref('')
// 判断当前用户是否为管理员
const isAdmin = computed(() => {
  const roleName = useUserStore().roles[0]
  console.log('roleNameroleNameroleNameroleNameroleName', roleName)
  return roleName === 'admin' || roleName === 'TAdmin' || roleName === 'DAdmin'
})
// 获取所有用户列表
const getAllUsers = async() => {
  try {
    const params = {
      pageSize: 10000
    }
    // 这里是获取所有用户的接口，后续需要替换为真实接口
    const res = await request({
      url: '/cms/system/user/list',
      method: 'get',
      params
    })
    userList.value = res.rows || []
    // 默认选中当前用户
    selectedUserId.value = useUserStore().userInfo.userId
  } catch (error) {
    console.error('获取用户列表失败', error)
  }
}

// 根据选择的用户ID或当前用户ID获取历史对话
const user = computed(() => {
  return isAdmin.value && selectedUserId.value ? selectedUserId.value : useUserStore().userInfo.userId
})

// 新建对话
const handleNewChat = () => {
  router.push('/')
}
// 处理用户切换
const handleUserChange = () => {
  currentId.value = ''
  router.replace('/history')
  getHistory()
}

// 获取历史列表
const historyList = ref([])
const hasHistory = computed(() => historyList.value.some(item => item.group?.length))
const getHistory = async() => {
  const url = '/dify/v1/conversations'
  const params = {
    user: user.value,
    limit: 100
  }
  const res = await request({
    url,
    method: 'get',
    params
  })
  // 刚开始时将历史对话列表直接赋值给对话列表
  historyList.value = groupDataByTime(res.data, 'created_at')
  if (res.data.length) {
    // 当切换用户时，始终选择第一条对话
    handleChooseHistory(res.data[0].id)
  }
}
// 选择历史对话
const handleChooseHistory = (id) => {
  if (id) {
    currentId.value = id
    chatPageRef.value.getHistory(id)
    router.replace(`/history?id=${id}`)
  } else {
    router.replace('/history')
  }
}
// 点击操作按钮
const handleCtrlClick = (id, e) => {
  console.log(`output->id`, id, e)
}
// 删除操作
const handleDelete = (id) => {
  proxy.$modal.confirm('确定要删除该条对话吗？', '提示').then(() => {
    request({
      url: `/dify/v1/conversations/${id}`,
      method: 'delete',
      data: {
        user: user.value
      }
    }).then(res => {
      proxy.$modal.msgSuccess('删除成功')
      if (route.query.id === id) {
        router.replace('/history')
      }
      getHistory()
    })
  }).catch(() => { })
}
// 根据chatType获取对应的icon
const getIconByChatType = (chatType) => {
  return agentList.find(item => item.chatType === chatType)?.icon
}

// 初始化
if (isAdmin.value) {
  getAllUsers()
}
getHistory()
</script>

<template>
  <div class="flex wh-full ">
    <div class="flex-[0_0_auto] bg-[rgba(255,255,255,0.9)] w-280px h-full border-r-1px border-r-solid border-#E5E6EB flex flex-col overflow-hidden">
      <div class="w-full h-48 px-16 py-8  flex items-center justify-between border-b-1px border-b-solid border-#E5E6EB flex-[0]">
        <div class="flex items-center gap-x-4px">
          <img src="@/assets/images/svg/icon_history_18.svg" style="width:18px;height:18px">
          <span class="text-16px font-500 text-#1D2129">历史对话</span>
        </div>
        <div class="flex flex-col gap-y-8px">
          <el-button
            plain
            class="h34 px-12 bg-#fff"
            color="#165DFF"
            @click="handleNewChat"
          >
            <svg-icon icon-class="chat" class="mr10 wh18" />
            新建对话
          </el-button>

        </div>
      </div>
      <!-- 用户选择下拉框 -->
      <div v-if="isAdmin" class="px-16 py-10 border-b-1px border-b-solid border-#E5E6EB">
        <el-select
          v-model="selectedUserId"
          placeholder="选择用户"
          size="default"
          class="w-full"
          filterable
          @change="handleUserChange"
        >
          <el-option
            v-for="item in userList"
            :key="item.userId"
            :label="item.nickName+'('+item.userName+')'"
            :value="item.userId"
          />
        </el-select>
      </div>
      <el-scrollbar class="px-16 pt10 flex-1">
        <template v-if="hasHistory">
          <div v-for="group, index in historyList" :key="index">
            <template v-if="group.group?.length">
              <div class="text-14 line-height-22 mb4">{{ group.label }}</div>
              <div
                v-for="item in group.group"
                :key="item.id"
                class="history-item w-full h-48 line-height-48px px-16 cursor-pointer mb-2 ellipsis border-0px border-solid border-transparent border-rd-8px position-relative color-#4E5969"
                :class="{active: item.id === currentId}"
                @click="handleChooseHistory(item.id)"
              >
                <svg-icon :icon-class="getIconByChatType(item.inputs.ChatType)" class="mr-5" />
                {{ item.name }}
                <el-popover trigger="click" width="100" popper-style="min-width: auto;">
                  <template #reference>
                    <div
                      class="item-ctrl w24 h24 position-absolute right-16 top-50% border-rd-4px f-c-c hidden cursor-pointer"
                      translate="y--50%"
                      @click.stop="handleCtrlClick(item.id, $event)"
                    >
                      <img class="w16 h16" :src="dots" alt="">
                    </div>
                  </template>
                  <div>
                    <div class="color-#4E5969 f-c-c text-16 cursor-pointer" @click="handleDelete(item.id)">
                      <img class="mr-5 w18 h18" :src="trash" alt="">
                      删除
                    </div>
                  </div>
                </el-popover>
              </div>
            </template>
          </div>
        </template>
        <el-empty v-else description="暂无数据" />
      </el-scrollbar>
    </div>
    <chat-page
      v-show="route.query.id"
      ref="chatPageRef"
      :user-id="user"
      show-new-chat
      class="w-full flex-1 bg-[rgba(255,255,255,0.8)]"
    />
    <div v-show="!historyList?.length" class="w-full flex-1 f-c-c">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.history-item {
  &:hover,
  &.active{
    // background-color: var(--el-color-primary-light-9);
    background-color: #E8F3FF;
    padding-right: 32px;
    .item-ctrl{
      display: flex;
      &:hover{
        // background-color: var(--el-color-primary-light-8);
        background-color: #E8F3FF;
      }
    }
  }
  &.active{
    color: #165DFF;
    // background-color: var(--el-color-primary-light-9);
    background-color: #E8F3FF;
    border-color: var(--el-color-primary-light-5);
  }
}
</style>
