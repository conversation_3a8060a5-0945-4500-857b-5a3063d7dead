<template>
  <div class="page-setting wh-full bg-[rgba(255,255,255,0)]">
    <div class="setting-box">
      <div class="setting-title">设置</div>
      <div class="setting-list-box">
        <div class="tips">账号</div>
        <div class="list-box">
          <div class="list-item line-bottom">
            <div class="list-item-title">头像</div>
            <div class="list-item-icon">
              <img src="@/assets/images/avatar2.png" alt="">
            </div>
          </div>
          <div class="list-item">
            <div class="list-item-title">用户名</div>
            <div class="list-item-icon">
              {{ userStore.userInfo?.nickName || "AI助手" }}
            </div>
          </div>
          <!-- <div class="list-item">
          <div class="list-item-title">密码修改</div>
          <div class="list-item-icon">
            <img style="width: 24px; height:24px" src="@/assets/images/svg/icon-right.svg" alt="">
          </div>
        </div> -->
        </div>
        <div class="list-box">
          <div class="list-item" style="cursor: pointer" @click="logout">
            <div class="list-item-title">退出登录</div>
            <div class="list-item-icon">
              <el-icon><ArrowRightBold /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import useUserStore from '@/store/modules/user'
import { getToken, removeToken } from '@/utils/auth'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
const userStore = useUserStore()
const tokenB = ref(!!getToken())
const router = useRouter()
const logout = () => {
  ElMessageBox.confirm('确认要退出吗?', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 调用退出登录接口
    userStore.logOut().then(() => {
      removeToken() // 移除本地存储的 token
      window.sessionStorage.setItem('routePath', '/')
      tokenB.value = getToken()
      router.push('/')
    })
    ElMessage({
      type: 'success',
      message: '退出成功'
    })
  })
}
</script>

<style scoped lang="scss">
.page-setting {
  margin: 0 auto;
  width: 100%;
  .setting-box {
    margin: 0 auto;
    width: 800px;
    .setting-title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
      letter-spacing: 0%;
      color: #1d2129;
      height: 64px;
      line-height: 64px;
    }
    .setting-list-box {
      opacity:0.8;
      .tips {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        letter-spacing: 0%;
        color: #86909c;
        height: 24px;
        line-height: 24px;
      }
      .list-box {
        margin-top: 8px;
        background: #f7f8fa;
        border-radius: 8px;
        padding: 0 16px;
        .list-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 56px;
          .list-item-title {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #000000e5;
          }
          .list-item-icon {
            img {
              width: 40px;
              height: 40px;
            }
          }
        }
        .line-bottom {
          border-bottom: 1px solid #e5e6eb;
        }
      }
    }
  }
}
</style>
