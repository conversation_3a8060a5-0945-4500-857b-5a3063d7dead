<script setup>
// import logoB from '@/assets/images/main-logo.png'
import useSettingStore from '@/store/modules/settings'
import SearchCom from '@/components/SearchCom'
import useChatStore from '@/store/modules/chat'
import { getToken } from '@/utils/auth'
import { emitter } from '@/plugins'
import NProgress from 'nprogress'
import PptDialog from '@/components/PptDialog'
const router = useRouter()
const chatStore = useChatStore()
chatStore.setChatType('chat') // 每次进入该页面都设置为chat类型
const query = ref('')
const title = import.meta.env.VITE_APP_TITLE

const settingStore = useSettingStore()

// 跳转到聊天页面开始聊天
const handleChat = (files, inputs, options) => {
  const params = {
    options: options,
    query: query.value,
    files
  }
  chatStore.setChatParams(params)
  router.push(`/chat`)
}
// 跳转聊天页面开始文生图
const handleImage = (options) => {
  const params = {
    options: options,
    query: query.value
  }
  chatStore.setImageParams(params)
  router.push(`/chat`)
}

// 文生PPT
const pptDialogVisible = ref(false)
const handlePPT = (options) => {
  if (getToken()) {
    const params = {
      options: options,
      query: query.value
    }
    chatStore.setPPTParams(params)
    pptDialogVisible.value = true
  } else {
    emitter.emit('showLoginDialog')
    NProgress.done()
  }
}
</script>

<template>
  <div class="page-home f-c-c-c wh-full">
    <div class="f-c-c p10 mb12">
      <img class="wh64" :src="settingStore.logoSrc" alt="">
      <div class="flex flex-col color-#1D2129 ml8">
        <div class="text-28 font-600 line-height-42">我是{{ title }}，很高兴见到你！</div>
        <div class="text-16 line-height-24 color-#909399">我可以帮你写代码、写作各种创意内容，请把你的任务交给我吧~</div>
      </div>
    </div>
    <search-com
      v-model="query"
      @on-chat="handleChat"
      @on-image="handleImage"
      @on-ppt="handlePPT"
    />
    <div class="text-14 color-#909399 line-height-22 text-center mt-40">
      <!-- 墨迹折射算法的微光，请持理性的火炬前行<br> -->
      内容由AI生成仅供参考
    </div>
    <PptDialog v-model="pptDialogVisible" :query="query" />
  </div>
</template>

<style scoped lang="scss">
.page-home{
  background-size: cover;
  background-position: center;
  // position: relative;
  // left: -64px;
}
.func-modules-box{
  margin-top: 40px;
  display: flex;
  gap:0 48px;
  .func-modules-item{
    display: flex;
    flex-direction: column;
    align-items: center;
    .func-modules-item-icon{
      cursor: pointer;
      width: 48px;
      height: 48px;
      background: #F7F8FA;
      border-radius: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      img{
        width: 24px;
        height: 24px;
      }
    }
    .func-modules-item-text{
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0%;
      color: #4E5969;
      margin-top: 8px;
    }
    .func-future{
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0%;
      color: #C9CDD4;
    }
  }
}
#container {
  width: 100%;
  height: calc(100vh - 120px);
  margin: 0;
  padding: 0;
  border-radius: 12px;
  // box-shadow: 0 0 12px rgba(120, 120, 120, 0.3);
  overflow: hidden;
  /* background: linear-gradient(-157deg, #f57bb0, #867dea); */
  background: #fff;
  color: white;
}
</style>
