<script setup>
// import logoB from '@/assets/images/main-logo.png'
import useSettingStore from '@/store/modules/settings'
import SearchCom from '@/components/SearchCom'
import useChatStore from '@/store/modules/chat'
import { ElNotification } from 'element-plus'

const chatStore = useChatStore()
chatStore.resetInputs() // 每次进入该页面都设置为chat类型
const router = useRouter()
const title = import.meta.env.VITE_APP_TITLE

const settingStore = useSettingStore()

// 跳转到聊天页面开始聊天
const handleChat = () => {
  router.push(`/chat`)
}

let notification = null
onMounted(() => {
  notification = ElNotification({
    title: 'AI助手2.0已上线！',
    message: h('div', { style: '' }, [
      h('p', {}, '1、上新9大智体，文生视频、文生音乐、文档生成、数据分析、图生视频等'),
      h('p', {}, '2、对话界面优化'),
      h('p', {}, '3、知识库优化'),
      h('p', {}, '点击下载操作手册')
    ]),
    type: 'success',
    duration: 0,
    onClick: () => {
      window.open('/profile/upload/static/AI%E6%99%BA%E8%83%BD%E5%8A%A9%E6%89%8B%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E2.0.docx')
    }
  })
})
onBeforeUnmount(() => {
  notification?.close()
})
</script>

<template>
  <div class="page-home f-c-c-c wh-full">
    <div class="f-c-c p10 mb12">
      <img class="wh64" :src="settingStore.logoSrc" alt="">
      <div class="flex flex-col color-#1D2129 ml8">
        <div class="text-28 font-600 line-height-42">我是{{ title }}，很高兴见到你！</div>
        <div class="text-16 line-height-24 color-#909399">我可以帮你写代码、写作各种创意内容，请把你的任务交给我吧~</div>
      </div>
    </div>
    <search-com
      @on-chat="handleChat"
    />
    <div class="text-14 color-#909399 line-height-22 text-center mt-40">
      <!-- 墨迹折射算法的微光，请持理性的火炬前行<br> -->
      内容由AI生成仅供参考
    </div>
  </div>
</template>

<style scoped lang="scss">
.page-home{
  background-size: cover;
  background-position: center;
  // position: relative;
  // left: -64px;
}
.func-modules-box{
  margin-top: 40px;
  display: flex;
  gap:0 48px;
  .func-modules-item{
    display: flex;
    flex-direction: column;
    align-items: center;
    .func-modules-item-icon{
      cursor: pointer;
      width: 48px;
      height: 48px;
      background: #F7F8FA;
      border-radius: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      img{
        width: 24px;
        height: 24px;
      }
    }
    .func-modules-item-text{
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0%;
      color: #4E5969;
      margin-top: 8px;
    }
    .func-future{
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0%;
      color: #C9CDD4;
    }
  }
}
#container {
  width: 100%;
  height: calc(100vh - 120px);
  margin: 0;
  padding: 0;
  border-radius: 12px;
  // box-shadow: 0 0 12px rgba(120, 120, 120, 0.3);
  overflow: hidden;
  /* background: linear-gradient(-157deg, #f57bb0, #867dea); */
  background: #fff;
  color: white;
}
</style>
