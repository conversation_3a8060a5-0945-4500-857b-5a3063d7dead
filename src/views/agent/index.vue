<script setup name="PageAgent">
import MainHeader from '@/components/MainHeader'
import AgentItem from './components/AgentItem.vue'
import { agentList } from '@/assets/dict'

const router = useRouter()
const hanldeNav = chatType => {
  if (!chatType) return
  router.push(`/agent/chat?type=${chatType}`)
}
</script>

<template>
  <div class="page-agent flex flex-col w-900 mx-auto mt-24">
    <main-header title="智能体" info="万象智联・灵犀一点" />
    <div class="flex flex-wrap">
      <template v-for="item, index in agentList" :key="index">
        <agent-item
          v-if="!item.hiddenNav"
          :data="item"
          @click="hanldeNav(item.chatType)"
        />
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
