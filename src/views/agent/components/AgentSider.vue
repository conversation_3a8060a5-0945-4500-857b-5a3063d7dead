<script setup name="AgentSider">
import { agentList } from '@/assets/dict'
import useChatStore from '@/store/modules/chat'

const { proxy } = getCurrentInstance()
const chatStore = useChatStore()
const route = useRoute()
const router = useRouter()

const handleNav = (type) => {
  if (!type) return
  chatStore.removeFileUpload()
  router.replace(`/agent/chat?type=${type}`)
}

const handleNewChat = (type) => {
  if (!type) return
  chatStore.removeFileUpload()
  proxy.$emitter.emit('agentNewChat', type)
}
</script>

<template>
  <div class="w-280 h-100vh border-r-1 border-r-#E5E6EB border-r-solid bg-[rgba(255,255,255,0.9)]">
    <div class="py-18 px-8 flex items-center text-16 font-500 line-height-24 border-b-1 border-b-#E5E6EB border-b-solid">
      <img class="mr-8 wh-18" src="@/assets/images/svg/icon_Ai.svg" alt="">
      智能体
    </div>
    <div class="p-8">
      <template v-for="item, index in agentList" :key="index">
        <div
          v-if="!item.hiddenNav"
          :class="['relative h-44 flex items-center border-rd-8 px-8 cursor-pointer', route.query.type === item.chatType ? 'bg-#E8F3FF color-#165DFF font-500' : 'color-#303133 font-400']"
          @click="handleNav(item.chatType)"
        >
          <svg-icon :icon-class="item.icon" :class="['wh-20 mr-8', route.query.type === item.chatType ? 'color-#165DFF' : 'color-#303133']" />
          <div class="text-14 line-height-22">{{ item.title }}{{ item.chatType ? '' : '（即将上线）' }}</div>
          <el-tooltip
            v-if="route.query.type === item.chatType"
            content="新建"
            :hide-after="0"
            :enterable="false"
            placement="top"
          >
            <div class="absolute right-8 wh-28 f-c-c p-4 border-rd-4px opacity-80 cursor-pointer hover:bg-#fff" @click.stop="handleNewChat(item.chatType)">
              <svg-icon icon-class="chat_new" />
            </div>
          </el-tooltip>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
</style>
