<script setup name="AgentItem">
defineProps({
  data: {
    type: Object,
    required: true
  }
})
</script>

<template>
  <div class="agent-item flex items-center bg-white border-rd-8 p-24 w284 h100 mt-24 [&:nth-child(3n+1)]:mr-24 [&:nth-child(3n+2)]:mr-24 cursor-pointer">
    <div class="wh-36 f-c-c border-rd-8 mr-16 bg-gradient-linear bg-gradient-[180deg,#165DFF_0%,#9CC7FF_100%]" flex="[0_0_auto]">
      <svg-icon :icon-class="data.icon" class="wh-20 color-white" />
    </div>
    <div class="flex flex-col ellipsis">
      <div class="text-18 font-500 line-height-26 color-#303133">{{ data.title }}{{ data.chatType ? '' : '（即将上线）' }}</div>
      <div class="mt-4 text-14 line-height-22 color-#909399 font-500 ellipsis">{{ data.info }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.agent-item{
  transition: all 0.3s ease;
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}
</style>
