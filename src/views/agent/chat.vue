<script setup name="AgentChat">
import AgentSider from './components/AgentSider.vue'
import ChatPage from '@/components/ChatPage'
import useChatStore from '@/store/modules/chat'

const chatStore = useChatStore()
const route = useRoute()

watchEffect(() => {
  chatStore.inputs.ChatType = route.query.type
  chatStore.inputs.model = 'DeepSeek'
  chatStore.inputs.query = ''
})

</script>

<template>
  <div class="agent-page h-full flex">
    <agent-sider />
    <div class="flex-[1]">
      <chat-page :key="route.query.type" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
</style>
