<script setup name="PageHeader">
import back from '@/assets/images/svg/icon_left.svg'
import light from '@/assets/images/svg/icon_light.svg'

const router = useRouter()

const handleBack = () => {
  router.back()
}
</script>

<template>
  <div class="page-header z-2 flex justify-between items-center px-10px h-40 position-absolute top-0 left-0 right-0">
    <div class="flex items-center text-14px line-height-22px cursor-pointer" @click="handleBack">
      <img class="w-16 h-16 mr-4" :src="back">
      返回
    </div>
    <!-- <div class="w-24 h-24 p-4 bg-#F7F8FA border-rd-4px cursor-pointer">
      <img :src="light" alt="">
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
</style>
