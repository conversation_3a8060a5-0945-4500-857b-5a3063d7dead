<template>
  <el-dialog
    v-model="dialogVisible"
    title="共享知识库"
    width="960px"
    style="margin-top: 25vh;"
    :before-close="handleClose"
    custom-class="share-dialog"
  >
    <div style="width:960px;height:1px;margin-left:-16px;margin-bottom:20px;background-color:#E5E6EB" />
    <!-- 知识库名称 -->
    <div class="kb-name-section">
      <div class="section-label">知识库名称</div>
      <div class="name-content">{{ knowledgeBaseName }}</div>
    </div>

    <!-- 邀请成员 -->
    <div class="invite-section">
      <div class="section-label">邀请成员</div>
      <div class="share-transfer">
        <!-- 左侧选择区域 -->
        <div class="transfer-left">
          <div class="count-info">{{ selectedCount }}项</div>
          <el-input
            v-model="searchKeyword"
            placeholder="请输入内容"
            class="search-input"
            clearable
            @input="handleSearch"
            @clear="handleClear"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <div class="tree-container">
            <div v-if="!deptTree || deptTree.length === 0" class="empty-state">
              <span>暂无可选成员</span>
            </div>
            <template v-else>
              <div class="debug-info" style="margin-bottom: 8px; color: #909399; font-size: 12px;">
                部门数: {{ deptTree.length }}
              </div>
              <el-tree
                ref="treeRef"
                :data="deptTree"
                show-checkbox
                node-key="id"
                :props="defaultProps"
                :default-expand-all="true"
                :filter-node-method="filterNode"
                @check="handleCheck"
              >
                <template #default="{ node, data }">
                  <span class="custom-tree-node" @click="handleNodeClick(node, data)">
                    <span>{{ node.label }}</span>
                    <span v-if="!data.isUser && data.children && data.children.length" class="user-count">
                      ({{ data.children.filter(child => child.isUser).length }})
                    </span>
                  </span>
                </template>
              </el-tree>
            </template>
          </div>
        </div>

        <!-- 中间按钮 -->
        <div class="transfer-actions">
          <div class="action-button-wrapper">
            <el-button type="text" :disabled="!hasCheckedNodes" @click="moveToRight">
              <img src="@/assets/images/exchange.png" alt="向右" class="action-icon">
            </el-button>
          </div>
          <div class="action-button-wrapper">
            <el-button type="text" :disabled="!hasRightCheckedUsers" @click="moveToLeft">
              <img src="@/assets/images/exchange.png" alt="向左" class="action-icon rotate">
            </el-button>
          </div>
        </div>

        <!-- 右侧选中区域 -->
        <div class="transfer-right">
          <div class="count-info">
            {{ selectedUsers.length }}项
            <span v-if="selectedUsers.length > 0" class="clear-all" @click="clearAll">清空</span>
          </div>
          <el-input
            v-model="selectedSearchKeyword"
            placeholder="请输入内容"
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <div class="selected-container">
            <div v-if="selectedUsers.length === 0" class="empty-state">
              <span>暂无数据</span>
            </div>
            <div v-else class="selected-items">
              <!-- 全选区域 -->
              <div v-if="filteredSelectedUsers.length > 0" class="select-all-row" @click="toggleRightCheckAll(!isRightAllChecked)">
                <el-checkbox
                  v-model="isRightAllChecked"
                  :indeterminate="rightCheckedUserIds.length > 0 && !isRightAllChecked"
                  @change="toggleRightCheckAll"
                >全选</el-checkbox>
              </div>
              <!-- 用户列表 -->
              <div
                v-for="user in filteredSelectedUsers"
                :key="user.id"
                class="selected-item"
              >
                <div class="selected-item-content" @click="toggleUserChecked(user)">
                  <el-checkbox
                    v-model="user.checked"
                    @change="(val) => handleRightUserCheck(user.id, val)"
                  />
                  <div class="selected-item-name">{{ user.name }}</div>
                </div>
                <!-- <el-icon class="remove-icon" @click="moveUserToLeft(user.id)"><Delete /></el-icon> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="width:960px;height:1px;margin-left:-16px;margin-top:20px;background-color:#E5E6EB" />

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmShare">确认共享</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  knowledgeBaseId: {
    type: String,
    default: ''
  },
  knowledgeBaseName: {
    type: String,
    default: '未命名知识库'
  }
})

const emits = defineEmits(['update:visible', 'shareSuccess'])

// 对话框显示控制
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emits('update:visible', val)
})

// 树相关
const treeRef = ref(null)
const deptTree = ref([])
const defaultProps = {
  children: 'children',
  label: 'label'
}

// 搜索
const searchKeyword = ref('')
const selectedSearchKeyword = ref('')

// 选中数据
const selectedUsers = ref([])
// 右侧选中的用户ID
const rightCheckedUserIds = ref([])
const selectedCount = computed(() => {
  return treeRef.value ? treeRef.value.getCheckedNodes().length : 0
})

const hasCheckedNodes = computed(() => {
  return selectedCount.value > 0
})

// 右侧是否有选中的用户
const hasRightCheckedUsers = computed(() => {
  return rightCheckedUserIds.value.length > 0
})

// 格式化部门树数据
const formatDeptTree = (data) => {
  console.log('Original data:', data)
  if (!Array.isArray(data)) {
    console.warn('Input data is not an array:', data)
    return []
  }

  const formatNode = (node) => {
    if (!node) {
      console.warn('Null or undefined node encountered')
      return null
    }

    // 调试输出节点数据
    console.log('Processing node:', node)

    const formattedNode = {
      id: node.id || node.tenantId || node.userId,
      label: node.name || node.tenantName || node.userName || '未命名',
      isUser: node.isUser || node.type === 'user' || false,
      children: []
    }

    console.log('Formatted node:', formattedNode)

    // 处理用户数组 - 可能是 users 或 userList
    const userArray = node.users || node.userList || []
    if (Array.isArray(userArray)) {
      const userNodes = userArray.map(user => ({
        id: user.tenantId || user.userId || user.id,
        label: user.tenantName || user.userName || user.name || '未命名',
        isUser: true
      })).filter(Boolean)
      formattedNode.children.push(...userNodes)
    }

    // 处理子部门数组 - 可能是 children 或 deptList
    const childrenArray = node.children || node.deptList || []
    if (Array.isArray(childrenArray)) {
      const childNodes = childrenArray
        .map(child => formatNode(child))
        .filter(Boolean)
      formattedNode.children.push(...childNodes)
    }

    // 如果没有子节点，删除 children 属性
    if (formattedNode.children.length === 0) {
      delete formattedNode.children
    }

    return formattedNode
  }

  const formattedTree = data.map(node => formatNode(node)).filter(Boolean)
  console.log('Final formatted tree:', formattedTree)
  return formattedTree
}

// 获取待分享成员列表
const getDeptTree = async() => {
  try {
    console.log('Fetching members for knowledge base:', props.knowledgeBaseId)
    const res = await request({
      url: '/cms/knowledgebase/waitShareMembers',
      method: 'get',
      params: {
        knowledgeId: props.knowledgeBaseId
      }
    })
    console.log('API Response:', res)

    // 直接判断返回数据是否为数组
    if (Array.isArray(res)) {
      console.log('Processing array response:', res)
      deptTree.value = formatDeptTree(res)
    } else if (res && typeof res === 'object') {
      // 如果返回的是单个对象，将其转换为数组处理
      console.log('Processing single object response:', res)
      deptTree.value = formatDeptTree([res])
    } else {
      console.warn('Invalid response format:', res)
      deptTree.value = []
    }

    console.log('Final deptTree value:', deptTree.value)
  } catch (error) {
    console.error('获取待分享成员列表失败:', error)
    // 添加测试数据以便调试
    deptTree.value = [
      {
        id: '1',
        label: '技术部',
        children: [
          {
            id: 'user1',
            label: '张三',
            isUser: true
          },
          {
            id: 'user2',
            label: '李四',
            isUser: true
          }
        ]
      },
      {
        id: '2',
        label: '市场部',
        children: [
          {
            id: 'user3',
            label: '王五',
            isUser: true
          }
        ]
      }
    ]
  }
}

// 事件处理
function handleCheck(node, checkedInfo) {
  // 处理选中节点，只需记录选中状态，移动时处理
}

function moveToRight() {
  if (!treeRef.value) return

  const checkedNodes = treeRef.value.getCheckedNodes()
  const userNodes = checkedNodes.filter(node => node.isUser)

  console.log('Selected user nodes:', userNodes)

  // 添加未选中的用户
  userNodes.forEach(node => {
    if (!selectedUsers.value.some(user => user.id === node.id)) {
      selectedUsers.value.push({
        id: node.id,
        name: node.label,
        // 保存原始ID，确保提交时能使用正确的字段
        originalId: node.id,
        checked: false // 默认未选中
      })
    }
  })

  // 清除选中状态
  treeRef.value.setCheckedKeys([])
}

function moveToLeft() {
  if (rightCheckedUserIds.value.length === 0) return

  // 移除选中的用户
  selectedUsers.value = selectedUsers.value.filter(
    user => !rightCheckedUserIds.value.includes(user.id)
  )

  // 清空右侧选中状态
  rightCheckedUserIds.value = []

  ElMessage.success('已移除选中的用户')
}

function clearAll() {
  if (selectedUsers.value.length === 0) return

  // 直接清空已选择的用户
  selectedUsers.value = []
  // 清空右侧选中状态
  rightCheckedUserIds.value = []
  ElMessage.success('已清空选择列表')
}

function handleClear() {
  searchKeyword.value = ''
  if (treeRef.value) {
    treeRef.value.filter('')
  }
}

function handleClose() {
  selectedUsers.value = []
  searchKeyword.value = ''
  selectedSearchKeyword.value = ''
  if (treeRef.value) {
    treeRef.value.setCheckedKeys([])
  }
  dialogVisible.value = false
}

// 过滤选中的用户（基于搜索关键字）
const filteredSelectedUsers = computed(() => {
  if (!selectedSearchKeyword.value) return selectedUsers.value
  return selectedUsers.value.filter(user =>
    user.name.toLowerCase().includes(selectedSearchKeyword.value.toLowerCase())
  )
})

// 处理右侧用户选中状态变化
function handleRightUserCheck(userId, checked) {
  // 更新数组中的用户选中状态
  const userIndex = selectedUsers.value.findIndex(user => user.id === userId)
  if (userIndex !== -1) {
    selectedUsers.value[userIndex].checked = checked
  }

  if (checked) {
    // 添加到选中列表
    if (!rightCheckedUserIds.value.includes(userId)) {
      rightCheckedUserIds.value.push(userId)
    }
  } else {
    // 从选中列表移除
    rightCheckedUserIds.value = rightCheckedUserIds.value.filter(id => id !== userId)
  }
}

// 右侧全选/取消全选
function toggleRightCheckAll(checked) {
  // 更新所有可见用户的选中状态
  filteredSelectedUsers.value.forEach(user => {
    const userIndex = selectedUsers.value.findIndex(u => u.id === user.id)
    if (userIndex !== -1) {
      selectedUsers.value[userIndex].checked = checked
    }
  })

  if (checked) {
    // 全选 - 将所有可见用户添加到选中列表
    rightCheckedUserIds.value = filteredSelectedUsers.value.map(user => user.id)
  } else {
    // 取消全选 - 从选中列表中移除所有可见用户
    const visibleIds = new Set(filteredSelectedUsers.value.map(user => user.id))
    rightCheckedUserIds.value = rightCheckedUserIds.value.filter(id => !visibleIds.has(id))
  }
}

// 判断右侧是否全选
const isRightAllChecked = computed(() => {
  if (filteredSelectedUsers.value.length === 0) return false
  return filteredSelectedUsers.value.every(user =>
    rightCheckedUserIds.value.includes(user.id)
  )
})

// 确认共享
async function confirmShare() {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个共享成员')
    return
  }

  try {
    // 收集所有选中用户的ID，用逗号拼接
    const userIds = selectedUsers.value.map(user => user.originalId || user.id).join(',')
    console.log('Submitting user IDs:', userIds)

    const res = await request({
      url: '/cms/knowledgebase/addShare',
      method: 'post',
      data: {
        knowledgeId: props.knowledgeBaseId,
        tenantId: userIds
      }
    })

    if (res.code === 200) {
      ElMessage.success('共享成功')
      emits('shareSuccess')
      handleClose()
    } else {
      ElMessage.error(res.msg || '共享失败，请重试')
    }
  } catch (error) {
    console.error('共享失败:', error)
    ElMessage.error('共享失败，请重试')
  }
}

// 过滤左侧树节点
const filterNode = (value, data) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase())
}

// 处理左侧搜索
function handleSearch() {
  if (treeRef.value) {
    treeRef.value.filter(searchKeyword.value)
  }
}

// 初始化
onMounted(() => {
  getDeptTree()
})

// 监听搜索关键字变化
watch(searchKeyword, (newVal) => {
  handleSearch()
})

// 当弹窗打开时，重新获取部门树
watch(() => props.visible, (val) => {
  if (val && props.knowledgeBaseId) {
    getDeptTree()
    // 重置搜索状态
    searchKeyword.value = ''
    selectedSearchKeyword.value = ''
  }
})

// 处理选中状态同步 - 监听 rightCheckedUserIds 变化
watch(rightCheckedUserIds, (newIds) => {
  // 遍历所有用户，更新其选中状态
  selectedUsers.value.forEach((user, index) => {
    const isChecked = newIds.includes(user.id)
    if (user.checked !== isChecked) {
      selectedUsers.value[index].checked = isChecked
    }
  })
}, { deep: true })

// 监听用户选中状态变化
watch(() => [...selectedUsers.value], (newUsers) => {
  newUsers.forEach((user, index) => {
    const isInCheckedList = rightCheckedUserIds.value.includes(user.id)
    if (user.checked && !isInCheckedList) {
      // 用户被选中但不在列表中，添加
      rightCheckedUserIds.value.push(user.id)
    } else if (!user.checked && isInCheckedList) {
      // 用户未选中但在列表中，移除
      rightCheckedUserIds.value = rightCheckedUserIds.value.filter(id => id !== user.id)
    }
  })
}, { deep: true })

// 处理节点点击
function handleNodeClick(node, data) {
  // 切换节点的选中状态
  if (treeRef.value) {
    const currentKey = data.id
    const isCurrentChecked = treeRef.value.getCheckedKeys().includes(currentKey)

    if (isCurrentChecked) {
      treeRef.value.setChecked(currentKey, false)
    } else {
      treeRef.value.setChecked(currentKey, true)
    }
  }
}

// 切换用户选中状态
function toggleUserChecked(user) {
  // 反转选中状态
  const newState = !user.checked

  // 更新用户选中状态
  user.checked = newState

  // 同步到rightCheckedUserIds
  handleRightUserCheck(user.id, newState)
}

// 将用户移动到左侧（从右侧移除）
function moveUserToLeft(userId) {
  // 从右侧移除单个用户
  selectedUsers.value = selectedUsers.value.filter(user => user.id !== userId)

  // 同时从选中列表中移除
  if (rightCheckedUserIds.value.includes(userId)) {
    rightCheckedUserIds.value = rightCheckedUserIds.value.filter(id => id !== userId)
  }

  ElMessage.success('已移除用户')
}
</script>

<style scoped>
:deep(.share-dialog) {
  margin-top: 15vh !important;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin-right: 0;
  border-bottom: 1px solid #dcdfe6;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-dialog__body) {
  padding: 20px;
  box-sizing: border-box;
  width: 100%;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #dcdfe6;
}

.kb-name-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-right: 15px;
  min-width: 80px;
  line-height: 22px;
  white-space: nowrap;
}

.name-content {
  background-color: #f5f7fa;
  padding: 10px 16px;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  flex: 1;
}

.invite-section {
  display: flex;
  width: 100%;
  box-sizing: border-box;
}

.share-transfer {
  display: flex;
  height: 300px;
  border-radius: 4px;
  flex: 1;
}

.transfer-left,
.transfer-right {
  flex: 1;
  max-width: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.transfer-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 16px;
}

.action-button-wrapper {
  width: 40px;
  display: flex;
  justify-content: center;
}

.count-info {
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 14px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
}

.search-input {
  margin: 8px;
}

.tree-container,
.selected-container {
  flex: 1;
  overflow: auto;
  padding: 8px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
}

.selected-items {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 5px;
}

.selected-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  flex: 1;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.selected-item-content:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.selected-item-name {
  margin-left: 4px;
  user-select: none;
}

.select-all-row {
  padding: 8px 12px;
  margin-bottom: 8px;
  margin-top: -16px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
}

.select-all-row:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.remove-icon {
  cursor: pointer;
  color: #909399;
}

.remove-icon:hover {
  color: #F56C6C;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.rotate {
  transform: rotate(180deg);
}

:deep(.el-button.is-text) {
  color: #409EFF;
  font-size: 18px;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-button.is-text[disabled]) {
  color: #c0c4cc;
  border-color: #ebeef5;
}

:deep(.el-tree-node__content) {
  height: 32px;
  padding-right: 8px;
}

:deep(.el-tree-node__children) {
  padding-left: 20px;
}

.clear-all {
  color: #409EFF;
  cursor: pointer;
  font-size: 12px;
}

.clear-all:hover {
  text-decoration: underline;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  cursor: pointer;
  width: 100%;
  height: 100%;
}

.custom-tree-node:hover {
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

.user-count {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.debug-info {
  margin-bottom: 8px;
  color: #909399;
  font-size: 12px;
}
</style>
