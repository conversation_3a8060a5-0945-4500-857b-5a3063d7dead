<template>
  <div class="knowledge-base ">
    <el-row :style="{height: isSidebarOpen ? '100vh' : 'calc(100vh - 32px)'}">
      <!-- 左侧菜单栏 -->
      <div class="sidebar bg-[rgba(255,255,255,0.9)]" :style="{width: isSidebarOpen ? '280px' : '0'}">
        <div v-if="isSidebarOpen" class="sidebar-content">
          <div class="sidebar-header">
            <span class="header-title">知识库</span>
            <!-- <el-button class="toggle-button" :title="'收起侧边栏'" @click="toggleSidebar">
              <img src="@/assets/images/svg/icon-unfold.svg" alt="">
            </el-button> -->
          </div>
          <el-menu
            :default-active="activeIndex"
            :collapse="!isSidebarOpen"
            :default-openeds="['1', '2', '3']"
            class="sidebar-menu"
          >
            <el-sub-menu index="1">
              <template #title>
                <div class="custom-submenu-title">
                  <span>个人知识库</span>
                  <el-tooltip content="新增知识库" placement="top">
                    <el-button
                      class="action-button"
                      text
                      @click.stop="handleAdd"
                    >
                      <img src="@/assets/images/svg/icon_add.svg" alt="">
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
              <el-menu-item
                v-for="(item, index) in knowledgeList"
                :key="index"
                class="library"
                :class="{'active-library': activeLibrary === item.id}"
                @click="handleLibraryClick(item)"
              >
                <div style="width:100%; display: flex; justify-content: space-between; align-items: center;">
                  <div style="display: flex; align-items: center;">
                    <!-- <img
                      v-if="activeLibrary === item.id"
                      style="padding-right: 4px;"
                      src="@/assets/images/svg/icon_folder_active.svg"
                      alt=""
                    >
                    <img
                      v-else
                      style="padding-right: 4px;"
                      src="@/assets/images/svg/icon_folder.svg"
                      alt=""
                    > -->
                    <img
                      :src="personalFileIcon"
                      alt=""
                      style="width:20px;height:20px;margin-right:8px"
                    >
                    {{ item.name }}

                  </div>
                  <el-tooltip content="删除" placement="top">
                    <img
                      src="@/assets/images/svg/icon_trash.svg"
                      alt=""
                      @click="handleDeleteDataset(item)"
                    >
                  </el-tooltip>
                </div>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="2">
              <template #title>
                <div class="custom-submenu-title">
                  <span>团队知识库</span>
                  <el-tooltip content="新增知识库" placement="top">
                    <el-button
                      class="action-button"
                      text
                      @click.stop="handleAdd"
                    >
                      <img src="@/assets/images/svg/icon_add.svg" alt="">
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
              <el-menu-item
                v-for="(item, index) in knowledgeListShare"
                :key="index"
                class="library"
                :class="{'active-library': activeLibrary === item.id}"
                @click="handleLibraryClick(item)"
              >
                <div style="width:100%; display: flex; justify-content: space-between; align-items: center;">
                  <div style="display: flex; align-items: center;">
                    <!-- <img
                      v-if="activeLibrary === item.id"
                      style="padding-right: 4px;"
                      src="@/assets/images/svg/icon_folder_team_active.svg"
                      alt=""
                    >
                    <img
                      v-else
                      style="padding-right: 4px;"
                      src="@/assets/images/svg/icon_folder_team.svg"
                      alt=""
                    > -->
                    <img
                      :src="teamFileIcon"
                      alt=""
                      style="width:20px;height:20px;margin-right:8px"
                    >
                    {{ item.name }}
                  </div>
                  <el-tooltip content="删除" placement="top">
                    <img
                      v-if="item.permission !== 'team' || item.belongMe"
                      src="@/assets/images/svg/icon_trash.svg"
                      alt=""
                      @click="handleDeleteDataset(item)"
                    >
                  </el-tooltip>
                </div>
              </el-menu-item>
            </el-sub-menu>

            <!-- 共享知识库菜单 -->
            <el-sub-menu index="3">
              <template #title>
                <div class="custom-submenu-title">
                  <span>共享知识库</span>
                  <el-badge v-if="pendingShareCount > 0" :value="pendingShareCount" class="share-badge" />
                </div>
              </template>
              <el-menu-item
                v-for="(item, index) in knowledgeListShared"
                :key="index"
                class="library"
                :class="{'active-library': activeLibrary === item.id}"
                @click="handleLibraryClick(item)"
              >
                <div style="width:100%; display: flex; justify-content: space-between; align-items: center;">
                  <div style="display: flex; align-items: center;">
                    <img
                      :src="shareFileIcon"
                      alt=""
                      style="width:20px;height:20px;margin-right:8px"
                    >
                    {{ item.name }}
                    <el-tag
                      v-if="item.shareHandleStatus === 'wait'"
                      size="mini"
                      type="warning"
                      style="margin-left:4px;margin-right: 4px;"
                    >待接收</el-tag>
                  </div>
                  <!-- 共享知识库操作按钮 -->
                  <div class="dropdown-buttons">
                    <el-dropdown trigger="click" @command="handleSharedCommand($event, item)">
                      <img
                        src="@/assets/images/svg/icon-more.svg"
                        alt=""
                        style="width:20px;height:20px;cursor:pointer"
                      >
                      <template #dropdown>
                        <el-dropdown-menu>
                          <!-- 待处理状态：显示同意/拒绝按钮 -->
                          <template v-if="item.shareHandleStatus === 'wait'">
                            <el-dropdown-item command="accept">
                              <div class="dropdown-item">
                                <el-icon style="color: #67C23A; margin-right: 8px;"><Check /></el-icon>
                                <span style="color: #67C23A; font-size: 14px; font-weight: 500;">同意加入</span>
                              </div>
                            </el-dropdown-item>
                            <el-dropdown-item command="reject">
                              <div class="dropdown-item">
                                <el-icon style="color: #F56C6C; margin-right: 8px;"><Close /></el-icon>
                                <span style="color: #F56C6C; font-size: 14px; font-weight: 500;">拒绝加入</span>
                              </div>
                            </el-dropdown-item>
                          </template>
                          <!-- 已加入状态：显示退出按钮 -->
                          <template v-else-if="item.shareHandleStatus === 'agree'">
                            <el-dropdown-item command="exit">
                              <div class="dropdown-item">
                                <img src="@/assets/images/svg/cancel.svg" style="height:18px;width:18px;" alt="exit">
                                <span style="color: #1296DB; font-size: 14px; font-weight: 500;">退出知识库</span>
                              </div>
                            </el-dropdown-item>
                          </template>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </div>
      </div>

      <!-- 右侧表格区域 -->
      <div class="main-content bg-[rgba(255,255,255,0.8)]">
        <div v-if="!isSidebarOpen" class="expand-button">
          <!-- <el-button class="toggle-button" :title="'展开侧边栏'" @click="toggleSidebar">
            <img src="@/assets/images/svg/icon-fold.svg" alt="">
          </el-button> -->
        </div>
        <div class="main-content-wrapper">
          <!-- 知识库标题 -->
          <div v-if="selectedLibrary.name" class="kb-header">
            <div class="kb-title-wrapper">
              <h2>{{ selectedLibrary.name }}</h2>
              <img
                v-if="selectedLibrary.permission !== 'team' || selectedLibrary.belongMe"
                src="@/assets/images/svg/knowledge-edit.svg"
                class="edit-icon"
                alt="edit"
                @click="handleEditLibrary"
              >
            </div>
            <div class="kb-description">
              {{ selectedLibrary.description || "主人很懒,什么也没有留下" }}
            </div>

            <!-- 添加右侧操作按钮区域 -->
            <div class="kb-header-actions">
              <!-- 分享按钮 - 仅对个人知识库显示 -->
              <el-tooltip v-if="selectedLibrary.permission === 'me'" content="分享" placement="top">
                <img
                  src="@/assets/images/svg/share-to.svg"
                  class="action-icon"
                  alt="share"
                  @click="handleShareKnowledge"
                >
              </el-tooltip>

              <!-- 查看已分享人员列表按钮 - 仅对个人知识库且已共享时显示 -->
              <el-tooltip v-if="selectedLibrary.permission === 'me' && selectedLibrary.shareStatus" content="已分享人员" placement="top">
                <img
                  src="@/assets/images/svg/share-person-list.svg"
                  class="action-icon"
                  alt="shared members"
                  @click="handleShowSharedMembers"
                >
              </el-tooltip>
              <el-tooltip v-if="selectedLibrary.permission === 'team' && selectedLibrary.shareStatus && selectedLibrary.belongDept && selectedLibrary.belongMe" content="已分享部门" placement="top">
                <img
                  src="@/assets/images/svg/share-person-list.svg"
                  class="action-icon"
                  alt="shared members"
                  @click="handleShowSharedMembers"
                >
              </el-tooltip>

              <!-- 更多操作按钮 -->
              <el-dropdown trigger="click" @command="handleKbCommand">
                <img
                  src="@/assets/images/svg/icon-more.svg"
                  class="action-icon"
                  alt="more"
                >
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- 编辑知识库 -->
                    <el-dropdown-item
                      v-if="selectedLibrary.permission !== 'share'"
                      command="edit"
                      :disabled="(selectedLibrary.permission === 'team' && !selectedLibrary.belongMe) || !hasEditPermission(selectedLibrary)"
                    >
                      <div class="dropdown-item">
                        <img src="@/assets/images/svg/knowledge-edit.svg" style="padding-right:8px;" alt="edit">
                        <span
                          :style="{
                            color: (selectedLibrary.permission === 'team' && !selectedLibrary.belongMe) || !hasEditPermission(selectedLibrary) ? '#909399' : '#303133',
                            fontSize: '14px',
                            fontWeight: '500'
                          }"
                        >编辑</span>
                      </div>
                    </el-dropdown-item>

                    <!-- 分享知识库 - 仅对个人知识库或belongDept为true的团队知识库显示 -->
                    <el-dropdown-item
                      v-if="selectedLibrary.permission === 'me' || (selectedLibrary.permission === 'team' && selectedLibrary.belongDept && selectedLibrary.belongMe)"
                      command="share"
                    >
                      <div class="dropdown-item">
                        <img src="@/assets/images/svg/knowledge-share.svg" style="padding-right:8px;" alt="share">
                        <span
                          style="color: #303133; fontSize: 14px; fontWeight: 500;"
                        >共享</span>
                      </div>
                    </el-dropdown-item>

                    <!-- 删除知识库 -->
                    <el-dropdown-item
                      v-if="selectedLibrary.permission !== 'share'"
                      command="delete"
                      :disabled="(selectedLibrary.permission === 'team' && !selectedLibrary.belongMe) || !hasEditPermission(selectedLibrary)"
                    >
                      <div class="dropdown-item">
                        <img src="@/assets/images/svg/knowledge-trash.svg" style="padding-right:8px" alt="delete">
                        <span
                          :style="{
                            color: (selectedLibrary.permission === 'team' && !selectedLibrary.belongMe) || !hasEditPermission(selectedLibrary) ? '#909399' : '#F56C6C',
                            fontSize: '14px',
                            fontWeight: '500'
                          }"
                        >删除</span>
                      </div>
                    </el-dropdown-item>

                    <!-- 退出知识库 - 仅对共享知识库显示 -->
                    <el-dropdown-item
                      v-if="selectedLibrary.permission === 'share'"
                      command="exit"
                    >
                      <div class="dropdown-item">
                        <img src="@/assets/images/svg/cancel.svg" style="padding-right:8px;height:24px;width:24px;" alt="exit">
                        <span style="color: #1296DB; font-size: 14px; font-weight: 500;">退出知识库</span>
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <!-- 搜索和操作区 -->
          <div class="table-header">
            <div class="search-wrapper">
              <div class="search-row">
                <div class="search-item">
                  <div class="label">文件名</div>
                  <el-input
                    v-model="searchQuery"
                    placeholder="请输入文件名搜索"
                    clearable
                    @clear="clearSearch"
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                </div>
                <div class="search-item">
                  <div class="label">解析状态</div>
                  <el-select
                    v-model="parseStatus"
                    placeholder="解析状态"
                    clearable
                    @change="handleSearch"
                  >
                    <el-option label="未解析" value="UNSTART" />
                    <el-option label="解析中" value="RUNNING" />
                    <el-option label="成功" value="DONE" />
                    <el-option label="失败" value="FAIL" />
                    <el-option label="取消" value="CANCEL" />
                  </el-select>
                </div>
                <div class="search-buttons">
                  <el-button icon="RefreshRight" @click="resetSearch">重置</el-button>
                  <el-button icon="search" type="primary" @click="handleSearch">查询</el-button>
                </div>
              </div>
              <div class="action-row">
                <div v-if="hasEditPermission(selectedLibrary)" class="action-area">
                  <el-button style="background-color: #165DFF;" type="primary" @click="handleFileUpload">
                    <el-icon><Plus /></el-icon>上传文件
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 文件列表表格 -->
          <el-table
            v-loading="loading"
            :data="knowledgeDatasetList"
            style="width: 100%;background:transparent;"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <el-table-column
              prop="name"
              min-width="60"
              align="center"
              label="文件格式"
            >
              <template #default="scope">
                <div class="file-name-cell">
                  <img
                    :src="getFileIcon(scope.row.name)"
                    alt=""
                    style="width: 24px; height: 24px; margin-right: 8px;"
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="name" min-width="200" label="文件名">
              <template #default="scope">
                <div>
                  <!-- <img src="@/assets/images/svg/icon_pdf.svg" alt="" style="width: 24px; height: 24px; margin-right: 8px;"> -->
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              key="chunk_count"
              label="分块数"
              align="left"
              prop="chunk_count"
              width="120"
            />
            <el-table-column
              key="create_time"
              label="上传日期"
              align="left"
              prop="create_time"
              width="180"
            />
            <!--
            <el-table-column
              label="解析方法"
              align="left"
              width="120"
            >
              <template #default>
                <span>General</span>
              </template>
            </el-table-column> -->

            <!-- <el-table-column prop="size" label="解析状态" width="120">
              <template #default="scope">
                {{ scope.row.status==1?'成功':'' }}kb
              </template>
            </el-table-column> -->

            <el-table-column prop="size" label="大小" width="120">
              <template #default="scope">
                {{ formatFileSize(scope.row.size) }}
              </template>
            </el-table-column>
            <!-- <el-table-column prop="uploader" label="上传者" width="80">
              <template #default="scope">
                {{ scope.row.uploader || 'admin' }}
              </template>
            </el-table-column> -->
            <!-- <el-table-column prop="uploader" label="启用" width="80">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="1"
                  inactive-value="0"
                  @change="(val) => handleChange(val, scope.row)"
                />
              </template>
            </el-table-column> -->

            <el-table-column prop="uploader" label="解析状态" width="180">
              <template #default="scope">
                <el-tag :type="handleParseStatus(scope.row).value || 'info'">{{ handleParseStatus(scope.row).label || '' }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              width="150"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <div class="action-area">
                  <!-- 下载按钮固定位置 -->

                  <!-- 管理员可以操作所有知识库，普通用户只能操作个人知识库 -->
                  <template v-if="hasEditPermission(selectedLibrary)">
                    <!-- 解析按钮固定位置 -->
                    <div class="action-item">
                      <el-tooltip v-if="scope.row.run=='UNSTART' || scope.row.run=='FAIL'" content="解析" placement="top">
                        <img
                          src="@/assets/images/svg/icon_parse.svg"
                          alt=""
                          @click="parseDocuments(scope.row,false)"
                        >
                      </el-tooltip>
                      <el-tooltip v-if="scope.row.run=='CANCEL'" content="重新解析" placement="top">
                        <img
                          src="@/assets/images/svg/icon_refresh_green.svg"
                          alt=""
                          @click="parseDocuments(scope.row,true)"
                        >
                      </el-tooltip>
                      <el-tooltip v-if="scope.row.run=='RUNNING'" content="取消解析" placement="top">
                        <img
                          src="@/assets/images/svg/icon_cancel.svg"
                          alt=""
                          @click="stopParse(scope.row)"
                        >
                      </el-tooltip>
                    </div>
                    <!-- 删除按钮固定位置 -->
                    <div class="action-item">
                      <el-tooltip content="删除" placement="top">
                        <img
                          src="@/assets/images/svg/icon_trash.svg"
                          alt=""
                          @click="handleDelete(scope.row)"
                        >
                      </el-tooltip>
                    </div>
                  </template>
                  <div class="action-item">
                    <!-- 非管理员不可下载，显示不可下载图标 -->
                    <el-tooltip v-if="selectedLibrary.permission === 'team' && !(isAdmin || roleName === 'DAdmin')" content="非管理员不可下载" placement="top">
                      <img
                        src="@/assets/images/svg/icon_no_download.svg"
                        alt="不可下载"
                        style="opacity: 0.6;"
                      >
                    </el-tooltip>
                    <!-- 共享知识库的文件不允许下载，显示不可下载图标 -->
                    <!-- 非本部门的知识库也不允许下载 -->
                    <el-tooltip v-else-if="selectedLibrary.permission === 'share' || !hasEditPermission(selectedLibrary)" content="共享知识库文件不可下载" placement="top">
                      <img
                        src="@/assets/images/svg/icon_no_download.svg"
                        alt="不可下载"
                        style="opacity: 0.6;"
                      >
                    </el-tooltip>
                    <!-- 其他知识库的文件可以下载 -->
                    <el-tooltip v-else content="下载" placement="top">
                      <img
                        src="@/assets/images/svg/icon_download.svg"
                        alt=""
                        @click="handleDownload(scope.row)"
                      >
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-table-column>
            <!-- 空状态 -->
            <template #empty>
              <el-empty
                v-if="knowledgeDatasetList.length === 0"
                description="暂无数据"
              >
                <!-- <el-button type="primary" @click="handleFileUpload">上传文件</el-button> -->
              </el-empty>
            </template>
          </el-table>

          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>

        </div>
      </div>
    </el-row>
    <!-- 添加编辑知识库弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="form.id ? '编辑知识库' : '创建知识库'"
      width="640px"
      style="margin-top: 20vh"
      custom-class="create-dialog"
      :before-close="handleClose"
    >
      <div style="width:640px;height:1px;margin-left:-16px;margin-bottom:12px; background-color:#E5E6EB" />
      <div class="dialog-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="100px"
        >
          <el-form-item label="知识库类型">
            <el-radio-group v-model="form.permission" :disabled="!!form.id">
              <el-radio label="me">个人知识库</el-radio>
              <el-radio v-if="isAdmin || roleName === 'DAdmin'" label="team">团队知识库</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="知识库名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入知识库名称"
              maxlength="30"
              show-word-limit
              class="custom-input"
            />
          </el-form-item>

          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 5 }"
              placeholder="为知识库添加描述..."
              maxlength="200"
              show-word-limit
              class="custom-input"
            />
          </el-form-item>
        </el-form>
      </div>
      <div style="width:640px;height:1px;margin-left:-16px;background-color:#E5E6EB" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 上传文件弹窗 -->
    <el-dialog
      v-model="visible"
      title="上传文件"
      width="960px"
      style="margin-top: 25vh;"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      @close="handleCloseUpload"
    >
      <div class="upload-container">
        <!-- 上传区域 -->
        <el-upload
          ref="upload"
          class="upload-area"
          drag
          :limit="5"
          :action="uploadUrl"
          multiple
          :http-request="httpRequest"
          :before-upload="beforeUpload"
          :on-exceed="handleExceed"
          :on-progress="handleProgress"
          :auto-upload="false"
          :before-remove="handleRemove"
          @change="handleChange"
        >
          <div class="upload-content">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="upload-text">
              <span class="primary-text">点击或拖拽文件至此区域即可上传</span>
              <span class="sub-text">仅支持*.doc、*.docx、*.pdf、*.ppt、*.pptx、*.xls、*.xlsx、*.csv、*.txt格式(最多5个文件,单个文件不超过100M)</span>
            </div>
          </div>
        </el-upload>

        <!-- 文件列表 -->
        <div v-if="fileListUnUpload.length > 0" class="file-list">
          <el-table
            :border="false"
            :data="fileListUnUpload"
            style="width: 100%"
            class="custom-table"
          >
            <el-table-column
              prop="name"
              label="文件格式"
              align="center"
              width="180"
            >
              <template #default="scope">
                <img
                  :src="getFileIcon(scope.row.name)"
                  alt=""
                  style="width: 24px; height: 24px;margin-top: 4px;"
                >
              </template>
            </el-table-column>
            <el-table-column prop="name" label="文件名称" min-width="200" />
            <el-table-column prop="size" label="文件大小" width="120">
              <template #default="scope">
                {{ (scope.row.size / 1024).toFixed(2) }}kb
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-tooltip content="删除" placement="top">
                  <img
                    src="@/assets/images/svg/icon_trash.svg"
                    alt=""
                    style="cursor: pointer;"
                    @click="handleRemove(scope.row)"
                  >
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div style="width: 960px; height: 0.5px; margin-left: -16px; background-color: rgb(229, 230, 235);" />
      <!-- 底部按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button style="background-color:#F2F3F5" @click="handleCloseUpload">取消上传</el-button>
          <el-button
            type="primary"
            :loading="uploadLoading"
            :disabled="fileListUnUpload.length === 0"
            @click="submitUpload"
          >
            {{ uploadLoading ? '上传中...' : '开始上传' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
    <login-dialog
      v-model:visible="loginVisible"
      @login-success="handleLoginSuccess"
    />
    <!-- 添加已分享人员列表弹窗 -->
    <el-dialog
      v-model="sharedMembersVisible"
      title="知识库成员"
      width="800px"
      style="margin-top: 25vh;"
      destroy-on-close
    >

      <div class="dialog-content">
        <!-- 搜索区域 -->
        <!-- <div class="members-header">
          <div class="members-search">
            <el-input
              v-model="memberSearchQuery"
              placeholder="搜索成员"
              clearable
              @input="handleMemberSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div> -->
        <!-- 成员列表表格 -->
        <div style="width:800px;height:1px;margin-left:-16px;margin-bottom:16px;background-color:#E5E6EB" />

        <el-table
          ref="memberTableRef"
          v-loading="membersLoading"
          :data="filteredMembers"
          style="width: 100%; border: 1px solid #EBEEF5; border-radius: 8px;"
          :header-cell-style="{background:'#F5F7FA'}"
          :cell-style="{padding:'8px 0'}"
          row-key="tenantId"
          @selection-change="handleMemberSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="() => true"
            :reserve-selection="true"
          />
          <el-table-column label="部门" prop="fullDeptName" />
          <el-table-column label="姓名" prop="tenantName" />
          <el-table-column label="手机号" prop="phonenumber" />
          <el-table-column label="共享时间" prop="shareAcceptTime" width="180" />
          <el-table-column label="操作" width="100" align="center">
            <template #default="scope">
              <el-button
                type="text"
                @click="handleRemoveMember(scope.row)"
              >
                <el-icon color="#F56C6C"><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="width:800px;height:1px;margin-left:-16px;margin-top:20px;background-color:#E5E6EB" />
      </div>

      <!-- 添加底部操作区 -->
      <template #footer>
        <div class="dialog-footer">
          <!-- <el-button @click="sharedMembersVisible = false">取消</el-button> -->
          <el-button
            :disabled="selectedMembers.length === 0"
            @click="handleBatchRemove"
          >
            批量删除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 已分享部门列表弹窗 -->
    <el-dialog
      v-model="sharedDeptsVisible"
      title="已分享部门"
      width="800px"
      style="margin-top: 20vh;"
      destroy-on-close
    >
      <div class="dialog-content">
        <!-- 搜索区域 -->
        <!-- <div class="members-header">
          <div class="members-search">
            <el-input
              v-model="deptSearchQuery"
              placeholder="搜索部门"
              clearable
              @input="handleDeptSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div> -->
        <!-- 部门列表表格 -->
        <div style="width:800px;height:1px;margin-left:-16px;margin-bottom:16px;background-color:#E5E6EB" />

        <el-table
          ref="deptTableRef"
          v-loading="deptsLoading"
          :data="filteredDepts"
          style="width: 100%; border: 1px solid #EBEEF5; border-radius: 8px;"
          :header-cell-style="{background:'#F5F7FA'}"
          :cell-style="{padding:'8px 0'}"
          row-key="deptId"
          @selection-change="handleDeptSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="() => true"
            :reserve-selection="true"
          />
          <el-table-column label="部门名称" prop="deptName" />
          <el-table-column label="部门全称" prop="fullDeptName" />
          <el-table-column label="共享时间" prop="shareAcceptTime" width="180" />
          <el-table-column label="操作" width="100" align="center">
            <template #default="scope">
              <el-button
                type="text"
                :disabled="!selectedLibrary.belongMe"
                @click="selectedLibrary.belongMe && handleRemoveDept(scope.row)"
              >
                <template v-if="selectedLibrary.belongMe">
                  <el-icon color="#F56C6C"><Delete /></el-icon>
                </template>
                <template v-else>
                  <!-- 禁用状态下的删除图标 -->
                  <el-icon color="#F56C6C"><Delete /></el-icon>
                </template>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="width:800px;height:1px;margin-left:-16px;margin-top:20px;background-color:#E5E6EB" />
      </div>

      <!-- 添加底部操作区 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button
            :disabled="selectedDepts.length === 0"
            @click="handleBatchRemoveDepts"
          >
            批量删除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加共享弹窗 -->
    <ShareDialog
      v-if="shareDialogVisible"
      v-model:visible="shareDialogVisible"
      :knowledge-base-id="selectedLibrary.id"
      :knowledge-base-name="selectedLibrary.name"
      :knowledge-base-type="selectedLibrary.permission"
      @share-success="handleShareSuccess"
    />
  </div>
</template>

<script setup>
import LoginDialog from '@/components/LoginDialog/index.vue'
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
import { Plus, Search, UploadFilled, Check, Close, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import docSvg from '@/assets/icons/svg/doc/doc.svg'
import docxSvg from '@/assets/icons/svg/doc/docx.svg'
import pdfSvg from '@/assets/icons/svg/doc/pdf.svg'
import pptSvg from '@/assets/icons/svg/doc/ppt.svg'
import pptxSvg from '@/assets/icons/svg/doc/pptx.svg'
import txtSvg from '@/assets/icons/svg/doc/txt.svg'
import xlsSvg from '@/assets/icons/svg/doc/xls.svg'
import xlsxSvg from '@/assets/icons/svg/doc/xlsx.svg'
import personalFileIcon from '@/assets/images/svg/personal-file.svg'
import teamFileIcon from '@/assets/images/svg/team-file.svg'
import shareFileIcon from '@/assets/images/svg/share-file.svg'
import ShareDialog from '@/components/ShareDialog.vue'
const isSidebarOpen = ref(true)
const dialogVisible = ref(false)
const visible = ref(false)
const formRef = ref(null)
const activeIndex = ref('1')
const loading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const uploadLoading = ref(false) // 添加上传按钮loading状态
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
// 添加编辑相关的响应式变量
const editDialogVisible = ref(false)
const editFormRef = ref(null)
const editForm = ref({
  id: '',
  name: '',
  description: '',
  permission: 'me'
})

const getFileIcon = (fileName) => {
  const extension = fileName.split('.').pop()?.toLowerCase()
  const iconMap = {
    'doc': docSvg,
    'docx': docxSvg,
    'pdf': pdfSvg,
    'ppt': pptSvg,
    'pptx': pptxSvg,
    'txt': txtSvg,
    'xls': xlsSvg,
    'xlsx': xlsxSvg
  }
  return iconMap[extension] || docSvg // 默认返回 doc 图标
}
// 添加编辑相关的方法
const handleEditLibrary = () => {
  // 判断权限
  if (!hasEditPermission(selectedLibrary.value)) {
    ElMessage.warning('您没有权限修改该知识库')
    return
  }

  form.id = selectedLibrary.value.id
  form.name = selectedLibrary.value.name
  form.description = selectedLibrary.value.description || ''
  form.permission = selectedLibrary.value.permission
  dialogVisible.value = true
}

const handleEditClose = () => {
  editDialogVisible.value = false
  editForm.value = {
    id: '',
    name: '',
    description: '',
    permission: 'me'
  }
}

const handleEditSubmit = () => {
  if (!editFormRef.value) return
  editFormRef.value.validate(async(valid) => {
    if (valid) {
      try {
        const res = await request({
          url: `/knowledge/api/v1/datasets/${editForm.value.id}`,
          method: 'put',
          data: {
            name: editForm.value.name,
            description: editForm.value.description
          }
        })
        if (res.code === 0) {
          ElMessage.success('编辑成功')
          handleEditClose()
          // 更新知识库列表和当前选中的知识库
          getKnowledgeList()
          selectedLibrary.value.name = editForm.value.name
          selectedLibrary.value.description = editForm.value.description
        }
      } catch (error) {
        console.error('编辑失败:', error)
      }
    }
  })
}

const isEditingName = ref(false)
const isEditingDesc = ref(false)
const editingName = ref('')
const editingDesc = ref('')
const nameInputRef = ref(null)
const descInputRef = ref(null)
// 统一处理双击事件
const handleDbClick = (type) => {
  // 判断权限
  if (!hasEditPermission(selectedLibrary.value)) {
    ElMessage.warning('您没有权限修改该知识库')
    return
  }

  if (type === 'name') {
    editingName.value = selectedLibrary.value.name
    isEditingName.value = true
    nextTick(() => {
      nameInputRef.value?.focus()
    })
  } else if (type === 'desc') {
    editingDesc.value = selectedLibrary.value.description || ''
    isEditingDesc.value = true
    nextTick(() => {
      descInputRef.value?.focus()
    })
  }
}

const updateLibraryName = async() => {
  if (!editingName.value.trim()) {
    ElMessage.warning('知识库名称不能为空')
    editingName.value = selectedLibrary.value.name
    isEditingName.value = false
    return
  }

  try {
    const res = await request({
      url: `/knowledge/api/v1/datasets/${selectedLibrary.value.id}`,
      method: 'put',
      data: {
        name: editingName.value.trim()
      }
    })

    if (res.code === 0) {
      selectedLibrary.value.name = editingName.value.trim()
      // ElMessage.success('更新成功')
      // 更新左侧菜单列表
      getKnowledgeList()
    }
  } catch (error) {
    console.error('更新失败:', error)
    // ElMessage.error('更新失败')
  }
  isEditingName.value = false
}

const updateLibraryDesc = async() => {
  try {
    const res = await request({
      url: `/knowledge/api/v1/datasets/${selectedLibrary.value.id}`,
      method: 'put',
      data: {
        description: editingDesc.value.trim()
      }
    })

    if (res.code === 0) {
      selectedLibrary.value.description = editingDesc.value.trim()
      // ElMessage.success('更新成功')
      // 更新左侧菜单列表
      getKnowledgeList()
    }
  } catch (error) {
    console.error('更新失败:', error)
    // ElMessage.error('更新失败')
  }
  isEditingDesc.value = false
}

const form = reactive({
  id: '',
  name: '',
  description: '',
  permission: 'me'
})
const activeLibrary = ref(null)
// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}
const upload = ref(null)
const roleName = useUserStore().roles[0]

// 添加权限判断函数
const hasEditPermission = (item) => {
  // 超级管理员和团队管理员有全部权限
  if (roleName === 'admin' || roleName === 'TAdmin') return true

  // DAdmin可以编辑个人知识库和belongDept为true的团队知识库
  if (roleName === 'DAdmin') {
    if (item.permission === 'me') return true
    if (item.permission === 'team' && item.belongDept && item.belongMe) return true
    return false
  }

  // 普通用户可以编辑自己的个人知识库
  if (roleName === 'common' && item.permission === 'me') return true

  return false
}

// 管理员判断
const isAdmin = computed(() => {
  return roleName === 'admin' || roleName === 'TAdmin'
})

const selectedLibrary = ref({
  name: '',
  description: '',
  files: []
})

const dataset_id = ref('')
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API) // 上传文件服务器地址
console.log('uploadFileUrluuuuuuuuuuuuuuuuuu:', uploadUrl.value)
// const uploadUrl = ref('')
const searchQuery = ref('')
const knowledgeList = ref([])
const knowledgeListShare = ref([])
const knowledgeListShared = ref([]) // 共享知识库
const knowledgeDatasetList = ref([])

let timer = null // 定义一个变量来存储定时器

const loginVisible = ref(false)
const shareDialogVisible = ref(false)
const sharedMembersVisible = ref(false)
const sharedDeptsVisible = ref(false)
const membersLoading = ref(false)
const deptsLoading = ref(false)
const sharedMembers = ref([])
const sharedDepts = ref([])
const selectedMembers = ref([])
const selectedDepts = ref([])
const memberSearchQuery = ref('')
const deptSearchQuery = ref('')

const httpRequest = async(options) => {
  const { file, onProgress, onSuccess, onError } = options
  const formData = new FormData()
  formData.append('file', file)
  formData.append('dataSetId', dataset_id.value)
  formData.append('name', file.name)

  try {
    const response = await request({
      url: uploadUrl.value,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
        'zs2': useUserStore().userInfo?.zs2 || ''
      },
      onUploadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress({ percent })
        uploadProgress.value = percent
        if (percent === 100) {
          uploadStatus.value = 'success'
        } else {
          uploadStatus.value = ''
        }
      }
    })

    if (response.code === 0 || response.code === 200) {
      onSuccess(response)
      ElMessage.success('上传成功')
      // 上传成功后获取最新的文件列表
      await getKnowledgeFileList()
      // 获取刚上传的文件信息并自动解析
      const uploadedFile = response.data.data[0]
      if (uploadedFile && uploadedFile.id) {
        // 自动调用解析文档
        await parseDocuments({
          id: uploadedFile.id,
          dataset_id: dataset_id.value
        }, false)
      }
    } else {
      onError(new Error('上传失败'))
      ElMessage.error('上传失败')
    }
  } catch (error) {
    onError(error)
    uploadLoading.value = false
    ElMessage.error('上传失败')
  } finally {
    // 所有文件上传完成后，取消loading状态
    uploadLoading.value = false
  }
}

const submitUpload = (file) => {
  // 设置上传按钮为loading状态
  uploadLoading.value = true
  upload.value.submit()
}

const fileList = ref([]) // 用于存储已选择的文件列表

const fileListUnUpload = ref([])

const handleRemove = (file, fileList) => {
  fileListUnUpload.value = fileListUnUpload.value.filter(f => f.uid !== file.uid)
}

const handleChange = (file, fileList) => {
  // 更新 fileListUnUpload 数组
  fileListUnUpload.value = fileList.map(f => ({
    ...f,
    uid: f.uid || file.uid // 确保每个文件都有一个唯一的 uid
  }))
}

const handleCloseUpload = () => {
  visible.value = false
  uploadProgress.value = 0
  uploadStatus.value = ''
  fileListUnUpload.value = []
  uploadLoading.value = false
}

const beforeUpload = (file) => {
  // 定义允许的文件类型
  const allowedTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/pdf', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv', 'text/plain']

  // 检查文件类型
  const isAllowedType = allowedTypes.includes(file.type)

  // 检查文件大小（限制为 100MB）
  const isLt10M = file.size / 1024 / 1024 < 100

  if (!isAllowedType) {
    ElMessage.error('文件格式不支持，请上传 *.doc, *.docx, *.pdf, *.ppt, *.pptx, *.xls, *.xlsx, *.csv, *.txt 格式的文件!')
    uploadLoading.value = false
    return false
  }

  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 100MB!')
    uploadLoading.value = false
    return false
  }

  return true
}

// const ragFlowTenantId = useUserStore().userInfo?.ragFlowTenantId || ''
const handleDownload = async(row) => {
  try {
    // if (select.created_by !== ragFlowTenantId) {
    //   ElMessage.error('您没有权限下载该知识库的文件')
    //   return
    // }

    // console.log('111111111111111111111111111', selectedLibrary.value)
    // console.log('222222222222222222222222222', ragFlowTenantId, row)

    // if (selectedLibrary.value.created_by !== ragFlowTenantId) {
    //   ElMessage.error('您没有权限下载该知识库的文件')
    //   return
    // }

    // 创建下载链接
    // const url = `http://113.250.183.157:8036/prod-api/knowledge/api/v1/datasets/${row.dataset_id}/documents/${row.id}`
    const url = `/knowledge/api/v1/datasets/${row.dataset_id}/documents/${row.id}`

    // 发起下载请求
    const response = await request({
      url: url,
      method: 'get',
      responseType: 'blob', // 重要：指定响应类型为blob
      headers: {
        'zs2': useUserStore().userInfo?.zs2 || ''
      }
    })

    // 创建Blob对象
    const blob = new Blob([response], { type: 'application/octet-stream' })

    // 创建下载链接
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(blob)
    downloadLink.download = row.name // 使用文件原始名称

    // 触发下载
    document.body.appendChild(downloadLink)
    downloadLink.click()

    // 清理
    document.body.removeChild(downloadLink)
    URL.revokeObjectURL(downloadLink.href)

    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// function showLogin() {
//   loginVisible.value = true
// }

const handleProgress = (event, file, fileList) => {
  // console.log('sssssssssssssssssss:', event, file, fileList)
  fileList.value = fileList
  uploadProgress.value = event.percent
  if (event.percent === 100) {
    uploadStatus.value = 'success'
  } else {
    uploadStatus.value = ''
  }
}

function handleLoginSuccess() {
  location.reload()

  // 登录成功后的处理
  console.log('登录成功')
}

function handleFileUpload() {
  visible.value = true
}
function handleLibraryClick(item) {
  searchQuery.value = ''
  knowledgeDatasetList.value = []
  // 重置分页
  pagination.currentPage = 1
  pagination.pageSize = 10

  selectedLibrary.value = item
  console.log('点击的知识库:', selectedLibrary.value, item)

  activeLibrary.value = item.id // 设置当前激活的知识库

  dataset_id.value = item.id
  // uploadUrl.value = `http://113.250.183.157:8036/prod-api/knowledge/api/v1/datasets/${dataset_id.value}/documents`
  // uploadUrl.value = `${import.meta.env.VITE_APP_BASE_API}/knowledge/api/v1/datasets/${dataset_id.value}/documents`
  // uploadUrl.value = `/knowledge/api/v1/datasets/${dataset_id.value}/documents`
  uploadUrl.value = `/cms/app/zs2/upload/proxy`
  getKnowledgeFileList()
}

// 添加解析状态搜索
const parseStatus = ref('')

// 修改搜索方法
function handleSearch() {
  if (!dataset_id.value) return
  getKnowledgeFileList()
}
function resetSearch() {
  searchQuery.value = ''
  parseStatus.value = ''
  pagination.currentPage = 1
  getKnowledgeFileList()
}

const handleParseStatus = (row) => {
  const obj = {
    'UNSTART': { label: '未解析', value: 'info' },
    'DONE': { label: '成功', value: 'success' },
    'CANCEL': { label: '取消', value: 'warning' },
    'FAIL': { label: '失败', value: 'danger' },
    'RUNNING': { label: `${(row.progress * 100).toFixed(2)}% 解析中...`, value: 'success' }
  }
  return obj[row.run]
}

// 修改清空搜索的方法
function clearSearch() {
  searchQuery.value = ''
  pagination.currentPage = 1
  getKnowledgeFileList()
}

function toggleSidebar() {
  isSidebarOpen.value = !isSidebarOpen.value
}

function handleAdd() {
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
  form.id = ''
  form.name = ''
  form.description = ''
  form.permission = 'me'
}

async function handleDeleteDataset(item) {
  try {
    await ElMessageBox.confirm(
      '确定要删除该知识库吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // 这里添加删除文件的接口调用
    const res = await request({
      url: `/knowledge/api/v1/datasets`,
      method: 'delete',
      data: { ids: [item.id] }
    })
    if (res.code === 0) {
      ElMessage.success(`删除知识库${item.name}成功`)
      getKnowledgeList()
    }
  } catch {
    // 用户取消删除
  }
}

async function handleDelete(row) {
  try {
    await ElMessageBox.confirm(
      '确定要删除该文件吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // 这里添加删除文件的接口调用
    const res = await request(
      {
        url: `/knowledge/api/v1/datasets/${row.dataset_id}/documents/${row.id}/chunks`,
        method: 'delete',
        data: { }
      }
    )
    if (res.code === 0) {
      request({
        url: `/knowledge/api/v1/datasets/${row.dataset_id}/documents`,
        method: 'delete',
        data: { ids: [row.id] }
      }).then((res) => {
        if (res.code === 0) {
          ElMessage.success('删除文件成功')
          getKnowledgeFileList()
        }
      })

      // ElMessage.success('删除文件成功')
      // getKnowledgeFileList()
    }
  } catch {
    // 用户取消删除
  }
}
const formatFileSize = (size) => {
  if (size < 1024) {
    return size.toFixed(2) + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}
// 解析文档
const parseDocuments = async(data, del) => {
  const params = {
    document_ids: [data.id]
    // delete: del,
    // run: data.run
  }

  // await request({
  //   url: `/knowledge/api/v1/datasets/${dataset_id.value}/chunks`,
  //   method: 'delete',
  //   data: params
  // })

  if (del) {
    const resDelete = await request({
      url: `/knowledge/api/v1/datasets/${dataset_id.value}/documents/${data.id}/chunks`,
      method: 'delete',
      data: { }
      // data: { 'chunk_ids': [] }
    })
    if (resDelete.code === '102') {
      ElMessage.error('清空chunks失败', resDelete.message)
    }
  }

  const res = await request({
    url: `/knowledge/api/v1/datasets/${dataset_id.value}/chunks`,
    method: 'post',
    data: params
  })
  if (res.code === '102') {
    ElMessage.error('解析失败，请联系管理员')
  }
  console.log('解析文档:', res)
  getKnowledgeFileList()
}

// 暂停解析文档
const stopParse = async(data) => {
  const params = {
    document_ids: [data.id]
  }

  const res = await request({
    url: `/knowledge/api/v1/datasets/${dataset_id.value}/chunks`,
    method: 'delete',
    data: params
  })
  console.log('停止解析文档:', res)

  getKnowledgeFileList()
}

async function getKnowledgeFileList() {
  if (!dataset_id.value) return
  loading.value = true

  try {
    const url = `/knowledge/api/v1/datasets/${dataset_id.value}/documents`
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      keywords: searchQuery.value,
      run: parseStatus.value || undefined // 添加解析状态筛选
    }
    const res = await request({
      url,
      method: 'get',
      params
    })

    // 处理返回数据，格式化时间
    knowledgeDatasetList.value = res.data.docs.map(doc => ({
      ...doc,
      create_time: dayjs(doc.create_time).format('YYYY-MM-DD HH:mm:ss')
    }))
    pagination.total = res.data.total

    // 检查是否有正在解析的文档
    const index = res.data.docs.findIndex(item => item.progress !== 1)
    if (index !== -1 && res.data.docs.length > 0) {
      // 清除现有的定时器
      if (timer) {
        clearTimeout(timer)
      }
      // 设置新的定时器，每5秒刷新一次
      timer = setTimeout(() => {
        getKnowledgeFileList()
      }, 5000)
    } else {
      // 如果没有正在解析的文档，清除定时器
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
  } finally {
    loading.value = false
  }
}

function handleExceed(files, fileList) {
  ElMessage.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
}
// 添加分页处理方法
function handleSizeChange(val) {
  pagination.pageSize = val
  pagination.currentPage = 1
  getKnowledgeFileList()
}

function handleCurrentChange(val) {
  pagination.currentPage = val
  getKnowledgeFileList()
}
const knowledgeListAll = ref([])
async function getKnowledgeList() {
  // 根据当前筛选条件决定加载哪些数据
  await Promise.all([
    getTeamKnowledgeList(),
    getPersonalKnowledgeList(),
    getSharedKnowledgeList()
  ])

  // 处理路由参数和选中默认知识库
  // 获取路由参数
  const libraryId = route.query.id
  const permission = route.query.permission

  if (libraryId) {
    // 根据权限找到对应的知识库列表
    let targetList = []
    if (permission === 'me') {
      targetList = knowledgeList.value
    } else if (permission === 'share') {
      targetList = knowledgeListShared.value
    } else {
      targetList = knowledgeListShare.value
    }

    const targetLibrary = targetList.find(item => item.id === libraryId)

    if (targetLibrary) {
      // 找到对应的知识库，触发选中效果
      handleLibraryClick(targetLibrary)
    } else {
      // 如果没找到，则默认选中第一个
      if (knowledgeList.value.length > 0) {
        handleLibraryClick(knowledgeList.value[0])
      } else if (knowledgeListShare.value.length > 0) {
        handleLibraryClick(knowledgeListShare.value[0])
      } else if (knowledgeListShared.value.length > 0) {
        handleLibraryClick(knowledgeListShared.value[0])
      }
    }
  } else {
    // 没有路由参数时的默认行为
    if (knowledgeList.value.length > 0) {
      handleLibraryClick(knowledgeList.value[0])
    } else if (knowledgeListShare.value.length > 0) {
      handleLibraryClick(knowledgeListShare.value[0])
    } else if (knowledgeListShared.value.length > 0) {
      handleLibraryClick(knowledgeListShared.value[0])
    }
  }
}

// 获取团队知识库
const getTeamKnowledgeList = async() => {
  try {
    const teamRes = await request({
      url: '/cms/knowledgebase/list',
      method: 'get',
      params: { permission: 'team' }
    })

    // 注意接口直接返回数组 []
    if (Array.isArray(teamRes)) {
      // 处理返回的团队知识库数据
      knowledgeListShare.value = teamRes.map(item => ({
        ...item,
        permission: 'team',
        // 确保每个知识库项都有belongDept字段
        belongDept: item.belongDept || false
      }))
    } else if (teamRes.code === 0 && Array.isArray(teamRes.data)) {
      // 兼容可能的格式变化，如果返回 {code:0, data:[]} 格式
      knowledgeListShare.value = teamRes.data.map(item => ({
        ...item,
        permission: 'team',
        belongDept: item.belongDept || false
      }))
    } else {
      console.error('获取团队知识库返回格式错误:', teamRes)
      knowledgeListShare.value = []
    }
  } catch (error) {
    console.error('获取团队知识库列表失败:', error)
    knowledgeListShare.value = []
  }
}

// 获取个人知识库
const getPersonalKnowledgeList = async() => {
  try {
    const personalRes = await request({
      url: '/cms/knowledgebase/list',
      method: 'get',
      params: {
        permission: 'me',
        shareHandleStatus: 'agree'
      }
    })

    // 注意接口直接返回数组 []
    if (Array.isArray(personalRes)) {
      // 确保每个知识库项都有shareStatus字段
      knowledgeList.value = personalRes.map(item => ({
        ...item,
        shareStatus: item.shareStatus || false // 确保有shareStatus字段，默认为false
      }))
    } else if (personalRes.code === 0 && Array.isArray(personalRes.data)) {
      // 兼容可能的格式变化，如果返回 {code:0, data:[]} 格式
      knowledgeList.value = personalRes.data.map(item => ({
        ...item,
        shareStatus: item.shareStatus || false
      }))
    } else {
      console.error('获取个人知识库返回格式错误:', personalRes)
      knowledgeList.value = []
    }
  } catch (error) {
    console.error('获取个人知识库列表失败:', error)
    knowledgeList.value = []
  }
}

// 获取共享知识库
const getSharedKnowledgeList = async() => {
  try {
    const sharedRes = await request({
      url: '/cms/knowledgebase/list',
      method: 'get',
      params: {
        permission: 'share',
        shareHandleStatus: 'agree'
      }
    })

    // 注意接口直接返回数组 []
    if (Array.isArray(sharedRes)) {
      // 处理返回的知识库数据
      knowledgeListShared.value = sharedRes.map(item => {
        // 确保每个项都有必要的属性
        const processedItem = {
          ...item,
          // permission可能是me或share，保持原值
          permission: item.permission || 'share',
          // 如果没有shareHandleStatus，设为默认值
          shareHandleStatus: item.shareHandleStatus || ''
        }
        return processedItem
      })
    } else if (sharedRes.code === 0 && Array.isArray(sharedRes.data)) {
      // 兼容可能的格式变化，如果返回 {code:0, data:[]} 格式
      knowledgeListShared.value = sharedRes.data.map(item => {
        const processedItem = {
          ...item,
          permission: item.permission || 'share',
          shareHandleStatus: item.shareHandleStatus || ''
        }
        return processedItem
      })
    } else {
      console.error('获取共享知识库返回格式错误:', sharedRes)
      knowledgeListShared.value = []
    }
  } catch (error) {
    console.error('获取共享知识库列表失败:', error)
    knowledgeListShared.value = []
  }
}

const handleSubmit = () => {
  if (!formRef.value) return
  formRef.value.validate(async(valid) => {
    if (valid) {
      try {
        const url = form.id ? '/knowledge/api/v1/datasets/' + form.id : '/knowledge/api/v1/datasets'
        const method = form.id ? 'put' : 'post'
        const res = await request({
          url,
          method,
          data: { name: form.name, description: form.description, permission: form.permission },
          headers: {
            'zs2': useUserStore().userInfo?.zs2 || ''
          }
        })
        if (res.code === 0) {
          ElMessage.success(form.id ? '编辑成功' : '创建成功')
          useUserStore().getKnowlegeLib()
          handleClose()
          getKnowledgeList()
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    }
  })
}

// 初始化加载知识库列表
getKnowledgeList()

// 设置定时器
onMounted(() => {
  getKnowledgeFileList()
})

// 清除定时器
onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})

// 计算待确认的共享知识库数量
const pendingShareCount = computed(() => {
  // 计算待接收的共享知识库数量
  return knowledgeListShared.value.filter(item =>
    (item.permission === 'share' || (item.permission === 'me' && item.shareHandleStatus)) &&
    item.shareHandleStatus === 'wait'
  ).length
})

// 处理共享知识库的命令
const handleSharedCommand = async(command, item) => {
  if (command === 'accept') {
    acceptShare(item)
  } else if (command === 'reject') {
    rejectShare(item)
  } else if (command === 'exit') {
    exitShare(item)
  }
}

// 同意加入共享知识库
const acceptShare = async(item) => {
  try {
    const res = await request({
      url: '/cms/knowledgebase/handleShare',
      method: 'post',
      data: {
        knowledgeId: item.id,
        status: 'agree'
      }
    })
    if (res.code === 0 || res.code === 200) {
      ElMessage.success('已同意加入知识库')
      // 更新当前知识库的状态为已加入
      item.shareHandleStatus = 'agree'
      getSharedKnowledgeList() // 刷新共享知识库列表
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 拒绝加入共享知识库
const rejectShare = async(item) => {
  try {
    const res = await request({
      url: '/cms/knowledgebase/handleShare',
      method: 'post',
      data: {
        knowledgeId: item.id,
        status: 'refuse'
      }
    })
    if (res.code === 0 || res.code === 200) {
      ElMessage.success('已拒绝加入知识库')
      // 从列表中移除该知识库
      knowledgeListShared.value = knowledgeListShared.value.filter(kb => kb.id !== item.id)
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 退出共享知识库
const exitShare = async(item) => {
  try {
    await ElMessageBox.confirm(
      '确定要退出该知识库吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    const res = await request({
      url: '/cms/knowledgebase/handleShare',
      method: 'post',
      data: {
        knowledgeId: item.id,
        status: 'quit'
      }
    })
    if (res.code === 0 || res.code === 200) {
      ElMessage.success('已成功退出知识库')
      // 从列表中移除该知识库
      knowledgeListShared.value = knowledgeListShared.value.filter(kb => kb.id !== item.id)
    }
  } catch (error) {
    if (error) {
      console.error('退出知识库失败:', error)
      ElMessage.error('操作失败')
    }
    // 用户取消操作，不做处理
  }
}

// 处理知识库操作命令
const handleKbCommand = (command) => {
  if (command === 'edit') {
    handleEditLibrary()
  } else if (command === 'share') {
    handleShareKnowledge()
  } else if (command === 'delete') {
    handleDeleteDataset(selectedLibrary.value)
  } else if (command === 'exit') {
    exitShare(selectedLibrary.value)
  }
}

// 处理分享知识库
const handleShareKnowledge = () => {
  if (!selectedLibrary.value || !selectedLibrary.value.id) {
    ElMessage.warning('请先选择知识库')
    return
  }

  shareDialogVisible.value = true
}

// 分享成功回调
const handleShareSuccess = () => {
  // 刷新知识库列表，标记当前知识库为已分享
  selectedLibrary.value.shareStatus = true
  getKnowledgeList()
}

// 显示已分享人员列表
const handleShowSharedMembers = async() => {
  if (!selectedLibrary.value || !selectedLibrary.value.id) {
    ElMessage.warning('请先选择知识库')
    return
  }

  // 根据知识库类型决定是显示已分享人员还是已分享部门
  const isTeamLibrary = selectedLibrary.value.permission === 'team'

  if (isTeamLibrary) {
    // 团队知识库 - 显示已分享部门
    deptsLoading.value = true
    sharedDeptsVisible.value = true
    selectedDepts.value = []

    try {
      const res = await request({
        url: '/cms/knowledgebase/sharedDepts',
        method: 'get',
        params: {
          knowledgeId: selectedLibrary.value.id
        }
      })

      if (Array.isArray(res)) {
        sharedDepts.value = res.map(item => ({
          ...item,
          shareTime: formatShareTime(item.shareTime),
          deptId: item.deptId || `dept_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` // 确保有唯一ID
        }))
      } else if (res.code === 0 && Array.isArray(res.data)) {
        sharedDepts.value = res.data.map(item => ({
          ...item,
          shareTime: formatShareTime(item.shareTime),
          deptId: item.deptId || `dept_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` // 确保有唯一ID
        }))
      } else {
        console.error('获取已分享部门列表返回格式错误:', res)
        sharedDepts.value = []
      }
    } catch (error) {
      console.error('获取已分享部门列表失败:', error)
      sharedDepts.value = []
    } finally {
      deptsLoading.value = false
    }
  } else {
    // 个人知识库 - 显示已分享人员
    membersLoading.value = true
    sharedMembersVisible.value = true
    selectedMembers.value = []

    try {
      const res = await request({
        url: '/cms/knowledgebase/sharedMembers',
        method: 'get',
        params: {
          knowledgeId: selectedLibrary.value.id
        }
      })

      if (Array.isArray(res)) {
        sharedMembers.value = res.map(item => ({
          ...item,
          shareTime: formatShareTime(item.shareTime),
          tenantId: item.tenantId || `tenant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` // 确保有唯一ID
        }))
      } else if (res.code === 0 && Array.isArray(res.data)) {
        sharedMembers.value = res.data.map(item => ({
          ...item,
          shareTime: formatShareTime(item.shareTime),
          tenantId: item.tenantId || `tenant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` // 确保有唯一ID
        }))
      } else {
        console.error('获取已分享人员列表返回格式错误:', res)
        sharedMembers.value = []
      }
    } catch (error) {
      console.error('获取已分享人员列表失败:', error)
      sharedMembers.value = []
    } finally {
      membersLoading.value = false
    }
  }
}

// 过滤后的成员列表
const filteredMembers = computed(() => {
  if (!memberSearchQuery.value) return sharedMembers.value

  return sharedMembers.value.filter(member =>
    member.name?.includes(memberSearchQuery.value) ||
    member.department?.includes(memberSearchQuery.value) ||
    member.phone?.includes(memberSearchQuery.value)
  )
})

// 过滤后的部门列表
const filteredDepts = computed(() => {
  if (!deptSearchQuery.value) return sharedDepts.value

  return sharedDepts.value.filter(dept =>
    dept.deptName?.includes(deptSearchQuery.value) ||
    dept.leader?.includes(deptSearchQuery.value)
  )
})

// 处理成员选择变化
const handleMemberSelectionChange = (selection) => {
  selectedMembers.value = selection
}

// 移除单个成员
const handleRemoveMember = async(member) => {
  try {
    await ElMessageBox.confirm(
      '确定要移除该成员吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await removeSharedMembers([member.tenantId])
  } catch {
    // 用户取消操作
  }
}

// 批量移除成员
const handleBatchRemove = async() => {
  if (selectedMembers.value.length === 0) {
    ElMessage.warning('请先选择要移除的成员')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要移除选中的 ${selectedMembers.value.length} 个成员吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const tenantIds = selectedMembers.value.map(member => member.tenantId)
    await removeSharedMembers(tenantIds)
  } catch {
    // 用户取消操作
  }
}

// 移除共享成员API调用
const removeSharedMembers = async(tenantIds) => {
  try {
    const res = await request({
      url: '/cms/knowledgebase/handleShare',
      method: 'post',
      data: {
        knowledgeId: selectedLibrary.value.id,
        status: 'revork',
        tenantId: tenantIds.join(',')
      }
    })

    if (res.code === 0 || res.code === 200) {
      ElMessage.success('已成功移除共享成员')
      // 移除成功后更新列表
      sharedMembers.value = sharedMembers.value.filter(member => !tenantIds.includes(member.tenantId))
      selectedMembers.value = []
    } else {
      ElMessage.error('移除失败: ' + (res.message || '未知错误'))
    }
  } catch (error) {
    console.error('移除共享成员失败:', error)
    ElMessage.error('操作失败')
  }
}

// 处理部门选择变化
const handleDeptSelectionChange = (selection) => {
  selectedDepts.value = selection
}

// 格式化分享时间函数
const formatShareTime = (time) => {
  if (!time) return '未知时间'

  try {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
  } catch (error) {
    return time
  }
}

// 移除单个部门
const handleRemoveDept = async(dept) => {
  try {
    await ElMessageBox.confirm(
      '确定要移除该部门吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await removeSharedDepts([dept.deptId])
  } catch {
    // 用户取消操作
  }
}

// 批量移除部门
const handleBatchRemoveDepts = async() => {
  if (selectedDepts.value.length === 0) {
    ElMessage.warning('请先选择要移除的部门')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要移除选中的 ${selectedDepts.value.length} 个部门吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const deptIds = selectedDepts.value.map(dept => dept.deptId)
    await removeSharedDepts(deptIds)
  } catch {
    // 用户取消操作
  }
}

// 移除共享部门API调用
const removeSharedDepts = async(deptIds) => {
  try {
    const res = await request({
      url: '/cms/knowledgebase/deleteShareDept',
      method: 'delete',
      data: {
        knowledgeId: selectedLibrary.value.id,
        status: 'revork',
        deptId: deptIds.join(',')
      }
    })

    if (res.code === 0 || res.code === 200) {
      ElMessage.success('已成功移除共享部门')
      // 移除成功后更新列表
      sharedDepts.value = sharedDepts.value.filter(dept => !deptIds.includes(dept.deptId))
      selectedDepts.value = []
    } else {
      ElMessage.error('移除失败: ' + (res.message || '未知错误'))
    }
  } catch (error) {
    console.error('移除共享部门失败:', error)
    ElMessage.error('操作失败')
  }
}
</script>

<style scoped lang="scss">

.custom-table {
  border: 1px solid var(--el-table-border-color);

  :deep(.el-table__inner-wrapper::before) {
    display: none;
  }

  :deep(.el-table__cell) {
    border-right: none !important;
    padding:0px;
  }

  :deep(.el-table__inner-wrapper) {
    border: none;
  }
}

.upload-container {
  padding: 20px;

  .upload-area {
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #F2F3F5;
    }
  }

  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;

    .el-icon--upload {
      font-size: 48px;
      color: #165DFF;
    }

    .upload-text {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .primary-text {
        font-size: 16px;
        color: #1F2937;
      }

      .sub-text {
        font-size: 12px;
        color: #DC362E;
      }
    }
  }

  .file-list {
    margin-top: 20px;
  }
}
/* 自定义 ElMessageBox 的按钮样式 */
.upload-progress {
  margin-top: 16px;
}
.library{
  box-sizing: border-box;
  height: 48px !important;
  margin: 4px 8px;
}
.library:hover{
  background: #E8F3FF !important;
  border-radius: 8px;
  margin: 4px 8px;
}
.active-library {
  background: #E8F3FF;
  margin: 4px 8px;
  border-radius: 8px;
}
:deep(.active-library) {
  color: #165DFF !important;
  box-sizing: border-box;
  height: 48px !important;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
}

:deep(.el-pagination .el-select .el-input) {
  width: 120px;
}

.knowledge-base {
  height: 100vh;
  overflow-y: scroll;
  // background-color: #fff;

  .expand-button{
    position: fixed;
    top: 0;
    left: 64px;
    transition: all 0.3s ease;
  }
}

.action-area {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px; // 设置图标之间的间距
}
.action-item {
  width: 24px; // 设置固定宽度
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    cursor: pointer;
  }
}

.table-header {
  margin-bottom: 16px;

  .search-wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .search-row {
      display: flex;
      align-items: center;
      gap: 64px;

      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .label {
          white-space: nowrap;
          color: #606266;
          font-size: 14px;
        }

        :deep(.el-input),
        :deep(.el-select) {
          width: 200px;
        }
      }

      .search-buttons {
        margin-left: auto;
        display: flex;
        gap: 8px;
      }
    }

    .action-row {
      display: flex;
      align-items: center;
    }
  }
}

.search-area {
  width: 300px;
  padding-right: 4px;
}

.file-name-cell {
  display: flex;
  justify-content:center;
  align-items: center;
}

.sidebar {
  height: 100vh;
  border-right: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  width: 320px;
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto; /* 修改为auto以启用滚动 */
  max-height: calc(100vh - 32px); /* 设置最大高度，确保内容过多时可以滚动 */
  /* 自定义滚动条样式 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(144, 147, 153, 0.3) transparent; /* Firefox */
}

/* 自定义Webkit浏览器的滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

.sidebar-header {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 0;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.toggle-button {
  padding: 6px;
  border: none;
  background: transparent;
}

.toggle-button:hover {
  background-color: #e5e7eb;
  border-radius: 4px;
}

.custom-submenu-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.action-button {
  padding: 2px;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.action-button:hover {
  background-color: #e5e7eb;
  border-radius: 4px;
}

.main-content {
  height: 100vh;
  overflow-y: auto;
  transition: all 0.3s ease;
  flex: 1;
}

.main-content-wrapper {
  padding: 24px;
}

.kb-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
  position: relative; /* 添加相对定位 */
}

.kb-header-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 16px; /* 调整图标间距 */
}

.action-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.action-icon:hover {
  opacity: 0.7;
}

.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.members-search {
  width: 240px;
}

.dropdown-item {
  display: flex;
  align-items: center;
}

.kb-title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;

  h2 {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 500;
    color: #1f2937;
  }

  .edit-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
    margin-bottom: 12px;
  }
}
:deep(.el-upload-list){
  display:none;
}
:deep(.el-dialog__footer){
  padding:0px;
}

.dialog-footer {
  margin-top: 0;
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
}

.share-badge {
  position: absolute;
  top: -18px;
  left: 98px;
}

:deep(.share-badge .el-badge__content) {
  background-color: #F56C6C;
  border: none;
}

.dropdown-buttons {
  display: flex;
  align-items: center;
}

// 添加成员列表表格样式
:deep(.el-table) {
  border: 1px solid #EBEEF5 !important;
  border-radius: 8px !important;
  overflow: hidden;
}

:deep(.el-table th.el-table__cell) {
  background-color: #F5F7FA;
}

:deep(.el-table::before) {
  display: none;
}

// 修复复选框点击问题
:deep(.el-checkbox__inner) {
  pointer-events: auto !important;
  cursor: pointer !important;
}

:deep(.el-table .cell .el-checkbox) {
  z-index: 2;
  position: relative;
}

:deep(.el-table__body tr.current-row > td) {
  background-color: transparent;
}

</style>
