<template>
  <div class="knowledge-base ">
    <el-row :style="{height: isSidebarOpen ? '100vh' : 'calc(100vh - 32px)'}">
      <!-- 左侧菜单栏 -->
      <div class="sidebar bg-[rgba(255,255,255,0.9)]" :style="{width: isSidebarOpen ? '280px' : '0'}">
        <div v-if="isSidebarOpen" class="sidebar-content">
          <div class="sidebar-header">
            <span class="header-title">知识库</span>
            <!-- <el-button class="toggle-button" :title="'收起侧边栏'" @click="toggleSidebar">
              <img src="@/assets/images/svg/icon-unfold.svg" alt="">
            </el-button> -->
          </div>
          <el-menu :default-active="activeIndex" :collapse="!isSidebarOpen" :default-openeds="['1', '2']">
            <el-sub-menu index="1">
              <template #title>
                <div class="custom-submenu-title">
                  <span>个人知识库</span>
                  <el-tooltip content="新增知识库" placement="top">
                    <el-button
                      class="action-button"
                      text
                      @click.stop="handleAdd"
                    >
                      <img src="@/assets/images/svg/icon_add.svg" alt="">
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
              <el-menu-item
                v-for="(item, index) in knowledgeList"
                :key="index"
                class="library"
                :class="{'active-library': activeLibrary === item.id}"
                @click="handleLibraryClick(item)"
              >
                <div style="width:100%; display: flex; justify-content: space-between; align-items: center;">
                  <div style="display: flex; align-items: center;">
                    <img
                      v-if="activeLibrary === item.id"
                      style="padding-right: 4px;"
                      src="@/assets/images/svg/icon_folder_active.svg"
                      alt=""
                    >
                    <img
                      v-else
                      style="padding-right: 4px;"
                      src="@/assets/images/svg/icon_folder.svg"
                      alt=""
                    >
                    {{ item.name }}
                  </div>
                  <el-tooltip content="删除" placement="top">
                    <img
                      src="@/assets/images/svg/icon_trash.svg"
                      alt=""
                      @click="handleDeleteDataset(item)"
                    >
                  </el-tooltip>
                </div>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="2">
              <template #title>
                <div class="custom-submenu-title">
                  <span>团队知识库</span>
                </div>
              </template>
              <el-menu-item
                v-for="(item, index) in knowledgeListShare"
                :key="index"
                class="library"
                :class="{'active-library': activeLibrary === item.id}"
                @click="handleLibraryClick(item)"
              >
                <div style="width:100%; display: flex; justify-content: space-between; align-items: center;">
                  <div style="display: flex; align-items: center;">
                    <img
                      v-if="activeLibrary === item.id"
                      style="padding-right: 4px;"
                      src="@/assets/images/svg/icon_folder_team_active.svg"
                      alt=""
                    >
                    <img
                      v-else
                      style="padding-right: 4px;"
                      src="@/assets/images/svg/icon_folder_team.svg"
                      alt=""
                    >
                    {{ item.name }}
                  </div>
                  <el-tooltip content="删除" placement="top">
                    <img
                      v-if="roleName === 'admin'||roleName === 'TAdmin'"
                      src="@/assets/images/svg/icon_trash.svg"
                      alt=""
                      @click="handleDeleteDataset(item)"
                    >
                  </el-tooltip>
                </div>
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </div>
      </div>

      <!-- 右侧表格区域 -->
      <div class="main-content bg-[rgba(255,255,255,0.8)]">
        <div v-if="!isSidebarOpen" class="expand-button">
          <!-- <el-button class="toggle-button" :title="'展开侧边栏'" @click="toggleSidebar">
            <img src="@/assets/images/svg/icon-fold.svg" alt="">
          </el-button> -->
        </div>
        <div class="main-content-wrapper">
          <!-- 知识库标题 -->
          <div v-if="selectedLibrary.name" class="kb-header">
            <h2
              v-if="!isEditingName"
              @dblclick="handleDbClick('name')"
            >
              {{ selectedLibrary.name }}
            </h2>
            <el-input
              v-else
              ref="nameInputRef"
              v-model="editingName"
              @blur="updateLibraryName"
              @keyup.enter="updateLibraryName"
            />

            <div
              v-if="!isEditingDesc"
              class="kb-description"
              @dblclick="handleDbClick('desc')"
            >
              {{ selectedLibrary.description || "主人很懒,什么也没有留下" }}
            </div>
            <el-input
              v-else
              ref="descInputRef"
              v-model="editingDesc"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              @blur="updateLibraryDesc"
              @keyup.enter="updateLibraryDesc"
            />
          </div>

          <!-- 搜索和操作区 -->
          <div class="table-header">
            <div class="search-area">
              <el-input
                v-model="searchQuery"
                placeholder="请输入文件名搜索"
                clearable
                @input="handleSearch"
                @clear="clearSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div v-if="roleName === 'admin' ||roleName === 'TAdmin'|| (roleName === 'common' && selectedLibrary.permission === 'me')" class="action-area">
              <el-button style="background-color: #165DFF;" type="primary" @click="handleFileUpload">
                <el-icon><Plus /></el-icon>上传文件
              </el-button>
            </div>
          </div>

          <!-- 文件列表表格 -->
          <el-table
            v-loading="loading"
            :data="knowledgeDatasetList"
            style="width: 100%;background:transparent;"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <el-table-column prop="name" min-width="200" label="文件名">
              <template #default="scope">
                <div>
                  <!-- <img src="@/assets/images/svg/icon_pdf.svg" alt="" style="width: 24px; height: 24px; margin-right: 8px;"> -->
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              key="chunk_count"
              label="分块数"
              align="left"
              prop="chunk_count"
              width="120"
            />
            <el-table-column
              key="create_time"
              label="上传日期"
              align="left"
              prop="create_time"
              width="180"
            />
            <!--
            <el-table-column
              label="解析方法"
              align="left"
              width="120"
            >
              <template #default>
                <span>General</span>
              </template>
            </el-table-column> -->

            <!-- <el-table-column prop="size" label="解析状态" width="120">
              <template #default="scope">
                {{ scope.row.status==1?'成功':'' }}kb
              </template>
            </el-table-column> -->

            <el-table-column prop="size" label="大小" width="120">
              <template #default="scope">
                {{ (scope.row.size / 1024).toFixed(2) }}kb
              </template>
            </el-table-column>
            <!-- <el-table-column prop="uploader" label="上传者" width="80">
              <template #default="scope">
                {{ scope.row.uploader || 'admin' }}
              </template>
            </el-table-column> -->
            <!-- <el-table-column prop="uploader" label="启用" width="80">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="1"
                  inactive-value="0"
                  @change="(val) => handleChange(val, scope.row)"
                />
              </template>
            </el-table-column> -->

            <el-table-column prop="uploader" label="解析状态" width="180">
              <template #default="scope">
                <el-tag :type="handleParseStatus(scope.row).value || 'info'">{{ handleParseStatus(scope.row).label || '' }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              width="150"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <div class="action-area">
                  <el-tooltip content="下载" placement="top">
                    <img
                      style="padding-right:8px"
                      src="@/assets/images/svg/icon_download.svg"
                      alt=""
                      @click="handleDownload(scope.row)"
                    >
                  </el-tooltip>
                  <!-- 管理员可以操作所有知识库，普通用户只能操作个人知识库 -->
                  <template v-if="roleName === 'admin' ||roleName === 'TAdmin'|| (roleName === 'common' && selectedLibrary.permission === 'me')">
                    <el-tooltip v-if="scope.row.run=='UNSTART' || scope.row.run=='FAIL'" content="解析" placement="top">
                      <img
                        style="padding-right:8px"
                        src="@/assets/images/svg/icon_parse.svg"
                        alt=""
                        @click="parseDocuments(scope.row,false)"
                      >
                    </el-tooltip>
                    <el-tooltip v-if="scope.row.run=='CANCEL'" content="重新解析" placement="top">
                      <img
                        style="padding-right:8px"
                        src="@/assets/images/svg/icon_refresh_green.svg"
                        alt=""
                        @click="parseDocuments(scope.row,true)"
                      >
                    </el-tooltip>
                    <el-tooltip v-if="scope.row.run=='RUNNING'" content="取消解析" placement="top">
                      <img
                        style="padding-right:8px"
                        src="@/assets/images/svg/icon_cancel.svg"
                        alt=""
                        @click="stopParse(scope.row)"
                      >
                    </el-tooltip>
                    <el-tooltip content="删除" placement="top">
                      <img
                        style="padding-right:8px"
                        src="@/assets/images/svg/icon_trash.svg"
                        alt=""
                        @click="handleDelete(scope.row)"
                      >
                    </el-tooltip>
                  </template>
                  <!-- 非管理员在团队知识库中不显示操作按钮 -->
                  <template v-else>
                    <span />
                  </template>
                </div>
              </template>
            </el-table-column>
            <!-- 空状态 -->
            <template #empty>
              <el-empty
                v-if="knowledgeDatasetList.length === 0"
                description="暂无数据"
              >
                <!-- <el-button type="primary" @click="handleFileUpload">上传文件</el-button> -->
              </el-empty>
            </template>
          </el-table>

          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>

        </div>
      </div>
    </el-row>

    <!-- 上传文件弹窗 -->
    <el-dialog
      v-model="visible"
      title="上传文件"
      width="960px"
      style="margin-top: 25vh;"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      @close="handleCloseUpload"
    >
      <div class="upload-container">
        <el-upload
          ref="upload"
          class="upload-demo"
          drag
          :limit="5"
          :action="uploadUrl"
          multiple
          :http-request="httpRequest"
          :before-upload="beforeUpload"
          :on-exceed="handleExceed"
          :on-progress="handleProgress"
          :auto-upload="false"
          :before-remove="handleRemove"
          @change="handleChange"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            点击或拖拽文件至此区域即可上传<br>
            <div style="color:#DC362E ;font-size: 10px;">仅支持*.doc、*.docx、*.pdf、*.ppt、*.pptx、*.xls、*.xlsx、*.csv、*.txt格式(最多5个文件,单个文件不超过10M)</div>
          </div>
        </el-upload>

        <div style="display: flex; justify-content: flex-end;">
          <el-button
            :disabled="fileListUnUpload.length === 0"
            style="background-color: #165DFF;"
            type="primary"
            @click="submitUpload"
          >
            开始上传
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 创建知识库弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      style="margin-top: 25vh;"
      title="创建知识库"
      width="400px"

      :before-close="handleClose"
      class="create-dialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="90px"
        class="dialog-form"
      >
        <el-form-item label-width="92" label="知识库名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入知识库名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入描述信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button style="background-color:#F2F3F5;color:#4E5969" @click="handleClose">取消</el-button>
          <el-button type="primary" style="background-color: #1D2129;color:#fff" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <login-dialog
      v-model:visible="loginVisible"
      @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup>
import LoginDialog from '@/components/LoginDialog/index.vue'
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Plus, Search, UploadFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
const isSidebarOpen = ref(true)
const dialogVisible = ref(false)
const visible = ref(false)
const formRef = ref(null)
const activeIndex = ref('1')
const loading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const isEditingName = ref(false)
const isEditingDesc = ref(false)
const editingName = ref('')
const editingDesc = ref('')
const nameInputRef = ref(null)
const descInputRef = ref(null)
// 统一处理双击事件
const handleDbClick = (type) => {
  // 判断权限
  if (!(roleName === 'admin' || roleName === 'TAdmin' || (roleName === 'common' && selectedLibrary.value.permission === 'me'))) {
    ElMessage.warning('您没有权限修改该知识库')
    return
  }

  if (type === 'name') {
    editingName.value = selectedLibrary.value.name
    isEditingName.value = true
    nextTick(() => {
      nameInputRef.value?.focus()
    })
  } else if (type === 'desc') {
    editingDesc.value = selectedLibrary.value.description || ''
    isEditingDesc.value = true
    nextTick(() => {
      descInputRef.value?.focus()
    })
  }
}

const updateLibraryName = async() => {
  if (!editingName.value.trim()) {
    ElMessage.warning('知识库名称不能为空')
    editingName.value = selectedLibrary.value.name
    isEditingName.value = false
    return
  }

  try {
    const res = await request({
      url: `/knowledge/api/v1/datasets/${selectedLibrary.value.id}`,
      method: 'put',
      data: {
        name: editingName.value.trim()
      }
    })

    if (res.code === 0) {
      selectedLibrary.value.name = editingName.value.trim()
      // ElMessage.success('更新成功')
      // 更新左侧菜单列表
      getKnowledgeList()
    }
  } catch (error) {
    console.error('更新失败:', error)
    // ElMessage.error('更新失败')
  }
  isEditingName.value = false
}

const updateLibraryDesc = async() => {
  try {
    const res = await request({
      url: `/knowledge/api/v1/datasets/${selectedLibrary.value.id}`,
      method: 'put',
      data: {
        description: editingDesc.value.trim()
      }
    })

    if (res.code === 0) {
      selectedLibrary.value.description = editingDesc.value.trim()
      // ElMessage.success('更新成功')
      // 更新左侧菜单列表
      getKnowledgeList()
    }
  } catch (error) {
    console.error('更新失败:', error)
    // ElMessage.error('更新失败')
  }
  isEditingDesc.value = false
}

const form = reactive({
  name: '',
  description: ''
})
const activeLibrary = ref(null)
// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}
const upload = ref(null)
const roleName = useUserStore().roles[0]
const selectedLibrary = ref({
  name: '',
  description: '',
  files: []
})

const dataset_id = ref('')
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API) // 上传文件服务器地址
console.log('uploadFileUrluuuuuuuuuuuuuuuuuu:', uploadUrl.value)
// const uploadUrl = ref('')
const searchQuery = ref('')
const knowledgeList = ref([])
const knowledgeListShare = ref([])
const knowledgeDatasetList = ref([])

let timer = null // 定义一个变量来存储定时器

const loginVisible = ref(false)

const httpRequest = async(options) => {
  const { file, onProgress, onSuccess, onError } = options
  const formData = new FormData()
  formData.append('file', file)
  formData.append('dataSetId', dataset_id.value)
  formData.append('name', file.name)

  try {
    const response = await request({
      url: uploadUrl.value,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
        'zs2': useUserStore().userInfo?.zs2 || ''
      },
      onUploadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress({ percent })
        uploadProgress.value = percent
        if (percent === 100) {
          uploadStatus.value = 'success'
        } else {
          uploadStatus.value = ''
        }
      }
    })

    if (response.code === 0 || response.code === 200) {
      onSuccess(response)
      ElMessage.success('上传成功')
      // 上传成功后获取最新的文件列表
      await getKnowledgeFileList()
      // 获取刚上传的文件信息并自动解析
      const uploadedFile = response.data.data[0]
      if (uploadedFile && uploadedFile.id) {
        // 自动调用解析文档
        await parseDocuments({
          id: uploadedFile.id,
          dataset_id: dataset_id.value
        }, false)
      }
    } else {
      onError(new Error('上传失败'))
      ElMessage.error('上传失败')
    }
  } catch (error) {
    onError(error)
    ElMessage.error('上传失败')
  }
}
const submitUpload = (file) => {
  upload.value.submit()
}

const fileList = ref([]) // 用于存储已选择的文件列表

const fileListUnUpload = ref([])

const handleRemove = (file, fileList) => {
  fileListUnUpload.value = fileListUnUpload.value.filter(f => f.uid !== file.uid)
}

const handleChange = (file, fileList) => {
  // 更新 fileListUnUpload 数组
  fileListUnUpload.value = fileList.map(f => ({
    ...f,
    uid: f.uid || file.uid // 确保每个文件都有一个唯一的 uid
  }))
}

const handleCloseUpload = () => {
  visible.value = false
  uploadProgress.value = 0
  uploadStatus.value = ''
  fileListUnUpload.value = []
}

const beforeUpload = (file) => {
  // 定义允许的文件类型
  const allowedTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/pdf', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv', 'text/plain']

  // 检查文件类型
  const isAllowedType = allowedTypes.includes(file.type)

  // 检查文件大小（限制为 10MB）
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    ElMessage.error('文件格式不支持，请上传 *.doc, *.docx, *.pdf, *.ppt, *.pptx, *.xls, *.xlsx, *.csv, *.txt 格式的文件!')
    return false
  }

  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  return true
}

// const ragFlowTenantId = useUserStore().userInfo?.ragFlowTenantId || ''
const handleDownload = async(row) => {
  try {
    // if (select.created_by !== ragFlowTenantId) {
    //   ElMessage.error('您没有权限下载该知识库的文件')
    //   return
    // }

    // console.log('111111111111111111111111111', selectedLibrary.value)
    // console.log('222222222222222222222222222', ragFlowTenantId, row)

    // if (selectedLibrary.value.created_by !== ragFlowTenantId) {
    //   ElMessage.error('您没有权限下载该知识库的文件')
    //   return
    // }

    // 创建下载链接
    // const url = `http://113.250.183.157:8036/prod-api/knowledge/api/v1/datasets/${row.dataset_id}/documents/${row.id}`
    const url = `/knowledge/api/v1/datasets/${row.dataset_id}/documents/${row.id}`

    // 发起下载请求
    const response = await request({
      url: url,
      method: 'get',
      responseType: 'blob', // 重要：指定响应类型为blob
      headers: {
        'zs2': useUserStore().userInfo?.zs2 || ''
      }
    })

    // 创建Blob对象
    const blob = new Blob([response], { type: 'application/octet-stream' })

    // 创建下载链接
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(blob)
    downloadLink.download = row.name // 使用文件原始名称

    // 触发下载
    document.body.appendChild(downloadLink)
    downloadLink.click()

    // 清理
    document.body.removeChild(downloadLink)
    URL.revokeObjectURL(downloadLink.href)

    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// function showLogin() {
//   loginVisible.value = true
// }

const handleProgress = (event, file, fileList) => {
  // console.log('sssssssssssssssssss:', event, file, fileList)
  fileList.value = fileList
  uploadProgress.value = event.percent
  if (event.percent === 100) {
    uploadStatus.value = 'success'
  } else {
    uploadStatus.value = ''
  }
}

function handleLoginSuccess() {
  location.reload()

  // 登录成功后的处理
  console.log('登录成功')
}

function handleFileUpload() {
  visible.value = true
}
function handleLibraryClick(item) {
  searchQuery.value = ''
  knowledgeDatasetList.value = []
  // 重置分页
  pagination.currentPage = 1
  pagination.pageSize = 10

  selectedLibrary.value = item
  console.log('点击的知识库:', selectedLibrary.value, item)

  activeLibrary.value = item.id // 设置当前激活的知识库

  dataset_id.value = item.id
  // uploadUrl.value = `http://113.250.183.157:8036/prod-api/knowledge/api/v1/datasets/${dataset_id.value}/documents`
  // uploadUrl.value = `${import.meta.env.VITE_APP_BASE_API}/knowledge/api/v1/datasets/${dataset_id.value}/documents`
  // uploadUrl.value = `/knowledge/api/v1/datasets/${dataset_id.value}/documents`
  uploadUrl.value = `/cms/app/zs2/upload/proxy`
  getKnowledgeFileList()
}

function handleSearch() {
  if (!dataset_id.value) return
  getKnowledgeFileList()
}

const handleParseStatus = (row) => {
  const obj = {
    'UNSTART': { label: '未解析', value: 'info' },
    'DONE': { label: '成功', value: 'success' },
    'CANCEL': { label: '取消', value: 'warning' },
    'FAIL': { label: '失败', value: 'danger' },
    'RUNNING': { label: `${(row.progress * 100).toFixed(2)}% 解析中...`, value: 'success' }
  }
  return obj[row.run]
}

// 修改清空搜索的方法
function clearSearch() {
  searchQuery.value = ''
  pagination.currentPage = 1
  getKnowledgeFileList()
}

function toggleSidebar() {
  isSidebarOpen.value = !isSidebarOpen.value
}

function handleAdd() {
  dialogVisible.value = true
}

function handleClose() {
  dialogVisible.value = false
  form.name = ''
  form.description = ''
}

async function handleDeleteDataset(item) {
  try {
    await ElMessageBox.confirm(
      '确定要删除该知识库吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // 这里添加删除文件的接口调用
    const res = await request({
      url: `/knowledge/api/v1/datasets`,
      method: 'delete',
      data: { ids: [item.id] }
    })
    if (res.code === 0) {
      ElMessage.success(`删除知识库${item.name}成功`)
      getKnowledgeList()
    }
  } catch {
    // 用户取消删除
  }
}

async function handleDelete(row) {
  try {
    await ElMessageBox.confirm(
      '确定要删除该文件吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // 这里添加删除文件的接口调用
    const res = await request(
      {
        url: `/knowledge/api/v1/datasets/${row.dataset_id}/documents/${row.id}/chunks`,
        method: 'delete',
        data: { }
      }
    )
    if (res.code === 0) {
      request({
        url: `/knowledge/api/v1/datasets/${row.dataset_id}/documents`,
        method: 'delete',
        data: { ids: [row.id] }
      }).then((res) => {
        if (res.code === 0) {
          ElMessage.success('删除文件成功')
          getKnowledgeFileList()
        }
      })

      // ElMessage.success('删除文件成功')
      // getKnowledgeFileList()
    }
  } catch {
    // 用户取消删除
  }
}

// 解析文档
const parseDocuments = async(data, del) => {
  const params = {
    document_ids: [data.id]
    // delete: del,
    // run: data.run
  }

  // await request({
  //   url: `/knowledge/api/v1/datasets/${dataset_id.value}/chunks`,
  //   method: 'delete',
  //   data: params
  // })

  if (del) {
    const resDelete = await request({
      url: `/knowledge/api/v1/datasets/${dataset_id.value}/documents/${data.id}/chunks`,
      method: 'delete',
      data: { }
      // data: { 'chunk_ids': [] }
    })
    if (resDelete.code === '102') {
      ElMessage.error('清空chunks失败', resDelete.message)
    }
  }

  const res = await request({
    url: `/knowledge/api/v1/datasets/${dataset_id.value}/chunks`,
    method: 'post',
    data: params
  })
  if (res.code === '102') {
    ElMessage.error('解析失败，请联系管理员')
  }
  console.log('解析文档:', res)
  getKnowledgeFileList()
}

// 暂停解析文档
const stopParse = async(data) => {
  const params = {
    document_ids: [data.id]
  }

  const res = await request({
    url: `/knowledge/api/v1/datasets/${dataset_id.value}/chunks`,
    method: 'delete',
    data: params
  })
  console.log('停止解析文档:', res)

  getKnowledgeFileList()
}

async function getKnowledgeFileList() {
  if (!dataset_id.value) return
  loading.value = true

  try {
    const url = `/knowledge/api/v1/datasets/${dataset_id.value}/documents`
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      name: searchQuery.value
    }
    const res = await request({
      url,
      method: 'get',
      params
    })
    knowledgeDatasetList.value = res.data.docs.map(doc => ({
      ...doc,
      create_time: dayjs(doc.create_time).format('YYYY-MM-DD HH:mm:ss')
    }))
    pagination.total = res.data.total // 假设接口返回了总数

    const index = res.data.docs.findIndex(item => item.progress !== 1)
    if (index !== -1 && res.data.docs.length > 0) {
      // 清除现有的定时器
      if (timer) {
        clearTimeout(timer)
      }
      // 设置新的定时器
      timer = setTimeout(() => {
        getKnowledgeFileList()
      }, 5000)
    } else {
      // 如果没有正在解析的文档，清除定时器
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    // ElMessage.error('获取文件列表失败')
  } finally {
    loading.value = false
  }
}

function handleExceed(files, fileList) {
  ElMessage.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
}
// 添加分页处理方法
function handleSizeChange(val) {
  pagination.pageSize = val
  pagination.currentPage = 1
  getKnowledgeFileList()
}

function handleCurrentChange(val) {
  pagination.currentPage = val
  getKnowledgeFileList()
}
const knowledgeListAll = ref([])
async function getKnowledgeList() {
  const url = '/knowledge/api/v1/datasets'
  const params = {
    page: 1,
    page_size: 100
  }
  const res = await request({
    url,
    method: 'get',
    params
  })
  knowledgeListAll.value = res.data
  knowledgeList.value = res.data.filter(item => item.permission === 'me')
  knowledgeListShare.value = res.data.filter(item => item.permission !== 'me')

  // 默认选中第一个知识库
  if (knowledgeList.value.length > 0) {
    handleLibraryClick(knowledgeList.value[0])
  } else if (knowledgeListShare.value.length > 0) {
    handleLibraryClick(knowledgeListShare.value[0])
  }
}

function handleSubmit() {
  if (!formRef.value) return
  formRef.value.validate(async(valid) => {
    if (valid) {
      const url = `/knowledge/api/v1/datasets`
      const params = {
        ...form
      }
      await request({
        url,
        method: 'post',
        data: params
      })

      ElMessage.success('创建成功')
      useUserStore().getKnowlegeLib()
      handleClose()
      getKnowledgeList()
    }
  })
}

// 初始化加载知识库列表
getKnowledgeList()

// 设置定时器
onMounted(() => {
  getKnowledgeFileList()
})

// 清除定时器
onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})
</script>

<style scoped lang="scss">

/* 自定义 ElMessageBox 的按钮样式 */
.upload-progress {
  margin-top: 16px;
}
.library{
  box-sizing: border-box;
  height: 48px !important;
  margin: 4px 8px;
}
.library:hover{
  background: #E8F3FF !important;
  border-radius: 8px;
  margin: 4px 8px;
}
.active-library {
  background: #E8F3FF;
  margin: 4px 8px;
  border-radius: 8px;
}
:deep(.active-library) {
  color: #165DFF !important;
  box-sizing: border-box;
  height: 48px !important;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
}

:deep(.el-pagination .el-select .el-input) {
  width: 120px;
}

.knowledge-base {
  height: 100vh;
  overflow-y: scroll;
  // background-color: #fff;

  .expand-button{
    position: fixed;
    top: 0;
    left: 64px;
    transition: all 0.3s ease;
  }
}

.action-area{
  display: flex;
  justify-content: center;
  align-items: center;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}

.search-area {
  width: 300px;
  padding-right: 4px;
}

.file-name-cell {
  display: flex;
  align-items: center;
}

.sidebar {
  height: 100vh;
  border-right: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  width: 320px;
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e7eb;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.toggle-button {
  padding: 6px;
  border: none;
  background: transparent;
}

.toggle-button:hover {
  background-color: #e5e7eb;
  border-radius: 4px;
}

.custom-submenu-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 20px;
}

.action-button {
  padding: 2px;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.action-button:hover {
  background-color: #e5e7eb;
  border-radius: 4px;
}

.main-content {
  height: 100vh;
  overflow-y: auto;
  transition: all 0.3s ease;
  flex: 1;
}

.main-content-wrapper {
  padding: 24px;
}

.kb-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.kb-header h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  cursor: pointer;
  padding: 4px;
}

.kb-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  cursor: pointer;
  padding: 4px;
}

:deep(.el-textarea__inner) {
  font-size: 14px;
}

:deep(.el-table) {
  border-radius: 8px;
  --el-table-border-color: #E5E7EB;
  --el-table-header-bg-color: #F9FAFB;
}

:deep(.el-table th) {
  font-weight: 600;
  color: #1F2937;
  background-color: #F9FAFB;
}

:deep(.el-table__row) {
  height: 60px;
}

:deep(.el-button--text) {
  padding: 0 4px;
}

:deep(.el-menu) {
  border-right: none;
  background: none;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-menu-item.is-active) {
  // background-color: #f0f9ff;
  color: #0ea5e9;
}

:deep(.el-sub-menu__icon-arrow) {
  display: none;
  margin-right: 240px;
}

:deep(.el-menu-item):hover,
:deep(.el-sub-menu__title):hover {
  background-color: #f0f9ff;
}

:deep(.el-sub-menu__title) {
  padding-right: 0 !important;
}

:deep(.create-dialog) {
  .el-form-item__label {
    white-space: nowrap;
  }

  .el-input {
    width: 260px;
  }
}

:deep(.dialog-footer .el-button) {
  border: none;
  padding: 8px 16px;
}

:deep(.el-message-box__btns .el-button--default) {
  background-color: #f2f3f5;
  color: #4e5969;
}

:deep(.el-message-box__btns .el-button--primary) {
  background-color: #1d2129;
  color: #fff;
}

.dialog-footer {
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
