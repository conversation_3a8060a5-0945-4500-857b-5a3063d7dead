<template>
  <el-dialog
    v-model="dialogVisible"
    title="共享知识库"
    width="960px"
    style="margin-top: 25vh;"
    :before-close="handleClose"
    custom-class="share-dialog"
  >
    <div style="width:960px;height:1px;margin-left:-16px;margin-bottom:20px;background-color:#E5E6EB" />
    <!-- 知识库名称 -->
    <div class="kb-name-section">
      <div class="section-label">知识库名称</div>
      <div class="name-content">{{ knowledgeBaseName }}</div>
    </div>

    <!-- 邀请成员 -->
    <div class="invite-section">
      <div class="section-label">{{ props.knowledgeBaseType === 'team' ? '邀请部门' : '邀请成员' }}</div>
      <div class="share-transfer">
        <!-- 左侧选择区域 -->
        <div class="transfer-left">
          <div class="count-info">{{ selectedCount }}项</div>
          <el-input
            v-model="searchKeyword"
            :placeholder="'请输入' + (props.knowledgeBaseType === 'team' ? '部门' : '成员') + '名称'"
            class="search-input"
            clearable
            @input="handleSearch"
            @clear="handleClear"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <div class="tree-container">
            <div v-if="!deptTree || deptTree.length === 0" class="empty-state">
              <span>暂无可选{{ props.knowledgeBaseType === 'team' ? '部门' : '成员' }}</span>
            </div>
            <template v-else>
              <div class="debug-info" style="margin-bottom: 8px; color: #909399; font-size: 12px;">
                部门数: {{ deptTree.length }}
              </div>
              <el-tree
                ref="treeRef"
                :data="deptTree"
                show-checkbox
                node-key="id"
                :props="defaultProps"
                :default-expand-all="true"
                :filter-node-method="filterNode"
                @check="handleCheck"
              >
                <template #default="{ node, data }">
                  <span class="custom-tree-node" @click="handleNodeClick(node, data)">
                    <span>{{ node.label }}</span>
                    <span v-if="!data.isUser && data.children && data.children.length" class="user-count">
                      ({{ data.children.filter(child => child.isUser).length }})
                    </span>
                  </span>
                </template>
              </el-tree>
            </template>
          </div>
        </div>

        <!-- 中间按钮 -->
        <div class="transfer-actions">
          <div class="action-button-wrapper">
            <el-button type="text" :disabled="!hasCheckedNodes" @click="moveToRight">
              <img src="@/assets/images/exchange.png" alt="向右" class="action-icon">
            </el-button>
          </div>
          <div class="action-button-wrapper">
            <el-button type="text" :disabled="!hasRightCheckedItems" @click="moveToLeft">
              <img src="@/assets/images/exchange.png" alt="向左" class="action-icon rotate">
            </el-button>
          </div>
        </div>

        <!-- 右侧选中区域 -->
        <div class="transfer-right">
          <div class="count-info">
            {{ selectedItems.length }}项
          </div>
          <el-input
            v-model="selectedSearchKeyword"
            :placeholder="'请输入' + (props.knowledgeBaseType === 'team' ? '部门' : '成员') + '名称'"
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <div class="selected-container">
            <div v-if="selectedItems.length === 0" class="empty-state">
              <span>暂无数据</span>
            </div>
            <div v-else class="selected-items">
              <!-- 全选区域 -->
              <div v-if="filteredSelectedItems.length > 0" class="select-all-row">
                <el-checkbox
                  v-model="isRightAllChecked"
                  :indeterminate="rightCheckedIds.length > 0 && !isRightAllChecked"
                  @change="toggleRightCheckAll"
                  @click.stop
                >全选</el-checkbox>
              </div>
              <!-- 项目列表 -->
              <div
                v-for="item in filteredSelectedItems"
                :key="item.id"
                class="selected-item"
              >
                <div class="selected-item-content" @click="toggleItemChecked(item)">
                  <el-checkbox
                    v-model="item.checked"
                    @click.stop
                    @change="(val) => handleRightItemCheck(item.id, val)"
                  />
                  <div class="selected-item-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="width:960px;height:1px;margin-left:-16px;margin-top:20px;background-color:#E5E6EB" />

    <template #footer>
      <div class="dialog-footer" style="padding: 20px 0 0 0 !important; margin: 0 !important;">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmShare">确认共享</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  knowledgeBaseId: {
    type: String,
    default: ''
  },
  knowledgeBaseName: {
    type: String,
    default: '未命名知识库'
  },
  knowledgeBaseType: {
    type: String,
    default: 'me'
  }
})

const emits = defineEmits(['update:visible', 'shareSuccess'])

// 对话框显示控制
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emits('update:visible', val)
})

// 树相关
const treeRef = ref(null)
const deptTree = ref([])
const defaultProps = {
  children: 'children',
  label: 'label'
}

// 搜索
const searchKeyword = ref('')
const selectedSearchKeyword = ref('')

// 选中数据
const selectedItems = ref([])
// 右侧选中的ID列表
const rightCheckedIds = ref([])
const selectedCount = computed(() => {
  return treeRef.value ? treeRef.value.getCheckedNodes().length : 0
})

const hasCheckedNodes = computed(() => {
  return selectedCount.value > 0
})

// 右侧是否有选中的项目
const hasRightCheckedItems = computed(() => {
  return rightCheckedIds.value.length > 0
})

// 格式化部门树数据
const formatDeptTree = (data) => {
  console.log('Original data:', data)
  if (!Array.isArray(data)) {
    console.warn('Input data is not an array:', data)
    return []
  }

  const formatNode = (node) => {
    if (!node) {
      console.warn('Null or undefined node encountered')
      return null
    }

    // 调试输出节点数据
    console.log('Processing node:', node)

    const formattedNode = {
      id: node.id || node.tenantId || node.userId,
      label: node.name || node.tenantName || node.userName || '未命名',
      isUser: node.isUser || node.type === 'user' || false,
      children: []
    }

    console.log('Formatted node:', formattedNode)

    // 处理用户数组 - 可能是 users 或 userList
    const userArray = node.users || node.userList || []
    if (Array.isArray(userArray)) {
      const userNodes = userArray.map(user => ({
        id: user.tenantId || user.userId || user.id,
        label: user.tenantName || user.userName || user.name || '未命名',
        isUser: true
      })).filter(Boolean)
      formattedNode.children.push(...userNodes)
    }

    // 处理子部门数组 - 可能是 children 或 deptList
    const childrenArray = node.children || node.deptList || []
    if (Array.isArray(childrenArray)) {
      const childNodes = childrenArray
        .map(child => formatNode(child))
        .filter(Boolean)
      formattedNode.children.push(...childNodes)
    }

    // 如果没有子节点，删除 children 属性
    if (formattedNode.children.length === 0) {
      delete formattedNode.children
    }

    return formattedNode
  }

  const formattedTree = data.map(node => formatNode(node)).filter(Boolean)
  console.log('Final formatted tree:', formattedTree)
  return formattedTree
}

// 获取待分享成员列表
const getDeptTree = async() => {
  // 如果 knowledgeBaseId 为空，则不发送请求
  if (!props.knowledgeBaseId) {
    console.warn('Knowledge base ID is empty, skipping API call')
    deptTree.value = []
    return
  }

  try {
    console.log('Fetching members for knowledge base:', props.knowledgeBaseId, 'type:', props.knowledgeBaseType)

    // 根据知识库类型选择不同的API接口
    const apiUrl = props.knowledgeBaseType === 'team'
      ? '/cms/knowledgebase/waitShareDepts' // 团队知识库 - 获取部门列表
      : '/cms/knowledgebase/waitShareMembers' // 个人知识库 - 获取成员列表

    const res = await request({
      url: apiUrl,
      method: 'get',
      params: {
        knowledgeId: props.knowledgeBaseId
      }
    })
    console.log('API Response:', res)

    // 直接判断返回数据是否为数组
    if (Array.isArray(res)) {
      console.log('Processing array response:', res)
      deptTree.value = formatDeptTree(res)
    } else if (res && typeof res === 'object') {
      // 如果返回的是单个对象，将其转换为数组处理
      console.log('Processing single object response:', res)
      deptTree.value = formatDeptTree([res])
    } else {
      console.warn('Invalid response format:', res)
      deptTree.value = []
    }

    console.log('Final deptTree value:', deptTree.value)
  } catch (error) {
    console.error('获取待分享列表失败:', error)
    // 添加测试数据以便调试
    if (props.knowledgeBaseType === 'team') {
      // 部门测试数据
      deptTree.value = [
        {
          id: 'dept1',
          label: '技术部',
          children: []
        },
        {
          id: 'dept2',
          label: '市场部',
          children: []
        },
        {
          id: 'dept3',
          label: '销售部',
          children: []
        }
      ]
    } else {
      // 用户测试数据
      deptTree.value = [
        {
          id: '1',
          label: '技术部',
          children: [
            {
              id: 'user1',
              label: '张三',
              isUser: true
            },
            {
              id: 'user2',
              label: '李四',
              isUser: true
            }
          ]
        },
        {
          id: '2',
          label: '市场部',
          children: [
            {
              id: 'user3',
              label: '王五',
              isUser: true
            }
          ]
        }
      ]
    }
  }
}

// 事件处理
function handleCheck(node, checkedInfo) {
  // 处理选中节点，只需记录选中状态，移动时处理
}

function moveToRight() {
  if (!treeRef.value) return

  const checkedNodes = treeRef.value.getCheckedNodes()

  // 根据知识库类型选择不同的节点筛选逻辑
  let nodesToAdd = []

  if (props.knowledgeBaseType === 'team') {
    // 团队知识库 - 选择部门节点
    // 部门节点没有isUser属性或isUser为false
    nodesToAdd = checkedNodes.filter(node => !node.isUser)
  } else {
    // 个人知识库 - 选择用户节点
    nodesToAdd = checkedNodes.filter(node => node.isUser)
  }

  console.log('Selected nodes to add:', nodesToAdd)

  // 添加未选中的节点到右侧
  nodesToAdd.forEach(node => {
    if (!selectedItems.value.some(item => item.id === node.id)) {
      selectedItems.value.push({
        id: node.id,
        name: node.label,
        // 保存原始ID，确保提交时能使用正确的字段
        originalId: node.id,
        checked: false // 默认未选中
      })
    }
  })

  // 清除选中状态
  treeRef.value.setCheckedKeys([])
}

function moveToLeft() {
  if (rightCheckedIds.value.length === 0) return

  // 移除选中的项目
  selectedItems.value = selectedItems.value.filter(
    item => !rightCheckedIds.value.includes(item.id)
  )

  // 清空右侧选中状态
  rightCheckedIds.value = []

  ElMessage.success('已移除选中的' + (props.knowledgeBaseType === 'team' ? '部门' : '成员'))
}

function handleClear() {
  searchKeyword.value = ''
  if (treeRef.value) {
    treeRef.value.filter('')
  }
}

function handleClose() {
  selectedItems.value = []
  searchKeyword.value = ''
  selectedSearchKeyword.value = ''
  if (treeRef.value) {
    treeRef.value.setCheckedKeys([])
  }
  dialogVisible.value = false
}

// 过滤选中的项目（基于搜索关键字）
const filteredSelectedItems = computed(() => {
  if (!selectedSearchKeyword.value) return selectedItems.value
  return selectedItems.value.filter(item =>
    item.name.toLowerCase().includes(selectedSearchKeyword.value.toLowerCase())
  )
})

// 处理右侧项目选中状态变化
function handleRightItemCheck(itemId, checked) {
  // 更新数组中的项目选中状态
  const itemIndex = selectedItems.value.findIndex(item => item.id === itemId)
  if (itemIndex !== -1) {
    selectedItems.value[itemIndex].checked = checked
  }

  if (checked) {
    // 添加到选中列表
    if (!rightCheckedIds.value.includes(itemId)) {
      rightCheckedIds.value.push(itemId)
    }
  } else {
    // 从选中列表移除
    rightCheckedIds.value = rightCheckedIds.value.filter(id => id !== itemId)
  }
}

// 右侧全选/取消全选
function toggleRightCheckAll(checked) {
  // 更新所有可见项目的选中状态
  filteredSelectedItems.value.forEach(item => {
    const itemIndex = selectedItems.value.findIndex(i => i.id === item.id)
    if (itemIndex !== -1) {
      selectedItems.value[itemIndex].checked = checked
    }
  })

  if (checked) {
    // 全选 - 将所有可见项目添加到选中列表
    rightCheckedIds.value = filteredSelectedItems.value.map(item => item.id)
  } else {
    // 取消全选 - 从选中列表中移除所有可见项目
    const visibleIds = new Set(filteredSelectedItems.value.map(item => item.id))
    rightCheckedIds.value = rightCheckedIds.value.filter(id => !visibleIds.has(id))
  }
}

// 判断右侧是否全选
const isRightAllChecked = computed(() => {
  if (filteredSelectedItems.value.length === 0) return false
  return filteredSelectedItems.value.every(item =>
    rightCheckedIds.value.includes(item.id)
  )
})

// 确认共享
async function confirmShare() {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请至少选择一个' + (props.knowledgeBaseType === 'team' ? '部门' : '共享成员'))
    return
  }

  try {
    // 收集所有选中项目的ID，用逗号拼接
    const selectedIds = selectedItems.value.map(item => item.originalId || item.id).join(',')
    console.log('Submitting IDs:', selectedIds)

    // 根据知识库类型选择不同的API接口和参数
    let apiUrl = ''
    let method = 'post'
    const requestData = {
      knowledgeId: props.knowledgeBaseId
    }

    if (props.knowledgeBaseType === 'team') {
      // 团队知识库 - 使用部门共享接口
      apiUrl = '/cms/knowledgebase/editShareDept'
      method = 'put' // 团队知识库使用PUT方法
      requestData.deptId = selectedIds
    } else {
      // 个人知识库 - 使用成员共享接口
      apiUrl = '/cms/knowledgebase/addShare'
      method = 'post' // 个人知识库使用POST方法
      requestData.tenantId = selectedIds
    }

    console.log('API URL:', apiUrl, 'Method:', method, 'Request Data:', requestData)

    const res = await request({
      url: apiUrl,
      method: method,
      data: requestData
    })

    if (res.code === 200 || res.code === 0) {
      ElMessage.success('共享成功')
      emits('shareSuccess')
      handleClose()
    } else {
      ElMessage.error(res.msg || '共享失败，请重试')
    }
  } catch (error) {
    console.error('共享失败:', error)
    ElMessage.error('共享失败，请重试')
  }
}

// 过滤左侧树节点
const filterNode = (value, data) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase())
}

// 处理左侧搜索
function handleSearch() {
  if (treeRef.value) {
    treeRef.value.filter(searchKeyword.value)
  }
}

// 初始化
onMounted(() => {
  // 只有在弹窗可见且有有效的知识库ID时才调用
  if (props.visible && props.knowledgeBaseId) {
    getDeptTree()
  }
})

// 监听搜索关键字变化
watch(searchKeyword, (newVal) => {
  handleSearch()
})

// 当弹窗打开时，重新获取部门树
watch(() => props.visible, (val) => {
  if (val && props.knowledgeBaseId) {
    getDeptTree()
    // 重置搜索状态
    searchKeyword.value = ''
    selectedSearchKeyword.value = ''
  }
})

// 处理选中状态同步 - 监听 rightCheckedIds 变化
watch(rightCheckedIds, (newIds) => {
  // 遍历所有项目，更新其选中状态
  selectedItems.value.forEach((item, index) => {
    const isChecked = newIds.includes(item.id)
    if (item.checked !== isChecked) {
      selectedItems.value[index].checked = isChecked
    }
  })
}, { deep: true })

// 监听项目选中状态变化
watch(() => [...selectedItems.value], (newItems) => {
  newItems.forEach((item, index) => {
    const isInCheckedList = rightCheckedIds.value.includes(item.id)
    if (item.checked && !isInCheckedList) {
      // 项目被选中但不在列表中，添加
      rightCheckedIds.value.push(item.id)
    } else if (!item.checked && isInCheckedList) {
      // 项目未选中但在列表中，移除
      rightCheckedIds.value = rightCheckedIds.value.filter(id => id !== item.id)
    }
  })
}, { deep: true })

// 处理节点点击
function handleNodeClick(node, data) {
  // 切换节点的选中状态
  if (treeRef.value) {
    const currentKey = data.id
    const isCurrentChecked = treeRef.value.getCheckedKeys().includes(currentKey)

    if (isCurrentChecked) {
      treeRef.value.setChecked(currentKey, false)
    } else {
      treeRef.value.setChecked(currentKey, true)
    }
  }
}

// 切换项目选中状态
function toggleItemChecked(item) {
  // 反转选中状态
  const newState = !item.checked

  // 更新项目选中状态
  item.checked = newState

  // 同步到rightCheckedIds
  handleRightItemCheck(item.id, newState)
}
</script>

<style scoped>
:deep(.share-dialog) {
  margin-top: 15vh !important;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin-right: 0;
  border-bottom: 1px solid #dcdfe6;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-dialog__body) {
  padding: 20px;
  box-sizing: border-box;
  width: 100%;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #dcdfe6;
}

/* 强制设置dialog-footer样式 */
:deep(.dialog-footer),
.dialog-footer {
  display: flex !important;
  justify-content: flex-end !important;
  margin: 0 !important;
  padding: 10px 0 0 0 !important;
}

.kb-name-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-right: 15px;
  min-width: 80px;
  line-height: 22px;
  white-space: nowrap;
}

.name-content {
  background-color: #f5f7fa;
  padding: 10px 16px;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  flex: 1;
}

.invite-section {
  display: flex;
  width: 100%;
  box-sizing: border-box;
}

.share-transfer {
  display: flex;
  height: 300px;
  border-radius: 4px;
  flex: 1;
}

.transfer-left,
.transfer-right {
  flex: 1;
  max-width: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.transfer-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 16px;
}

.action-button-wrapper {
  width: 40px;
  display: flex;
  justify-content: center;
}

.count-info {
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 14px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
}

.search-input {
  padding: 8px;
}

.tree-container,
.selected-container {
  flex: 1;
  overflow: auto;
  padding: 8px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
}

.selected-items {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 5px;
}

.selected-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  flex: 1;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.selected-item-content:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.selected-item-name {
  margin-left: 4px;
  user-select: none;
}

.select-all-row {
  padding: 8px 12px;
  margin-bottom: 8px;
  margin-top: -16px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
}

.select-all-row:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.remove-icon {
  cursor: pointer;
  color: #909399;
}

.remove-icon:hover {
  color: #F56C6C;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.rotate {
  transform: rotate(180deg);
}

:deep(.el-button.is-text) {
  color: #409EFF;
  font-size: 18px;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-button.is-text[disabled]) {
  color: #c0c4cc;
  border-color: #ebeef5;
}

:deep(.el-tree-node__content) {
  height: 32px;
  padding-right: 8px;
}

:deep(.el-tree-node__children) {
  padding-left: 20px;
}

.clear-all {
  color: #409EFF;
  cursor: pointer;
  font-size: 12px;
}

.clear-all:hover {
  text-decoration: underline;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  cursor: pointer;
  width: 100%;
  height: 100%;
}

.custom-tree-node:hover {
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

.user-count {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.debug-info {
  margin-bottom: 8px;
  color: #909399;
  font-size: 12px;
}
</style>
