<script setup name="ChatCom">
import useSettingsStore from '@/store/modules/settings'
import SearchFile from '@/components/SearchFile'
import { Marked } from 'marked'
import { markedHighlight } from 'marked-highlight'
import hljs from 'highlight.js'
import 'highlight.js/styles/atom-one-dark.css'
import copy from '@/assets/images/svg/icon_copy.svg'
import refresh from '@/assets/images/svg/icon_refresh.svg'
import { modelList } from '@/assets/dict'
import useChatStore from '@/store/modules/chat'

const { proxy } = getCurrentInstance()
const settingStore = useSettingsStore()
const chatStore = useChatStore()
const props = defineProps({
  query: { // 用户搜索内容
    type: String,
    default: ''
  },
  queryTime: {
    type: Number,
    default: null
  },
  inputs: { // 模型名称
    type: Object,
    default: () => {}
  },
  content: { // 回答内容
    type: String,
    default: ''
  },
  status: {
    type: String,
    default: 'normal'
  },
  files: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['on-refresh'])
const userFiles = computed(() => props.files.filter(item => item.belongs_to === 'user'))

// 给markdown添加highlight插件
const marked = new Marked(
  markedHighlight({
    emptyLangClass: 'hljs',
    langPrefix: 'hljs group language-',
    highlight(code, lang, info) {
      // const btnBox = `<div class="text-12px w-fit float-right hidden group-hover:block cursor-pointer border-1px border-solid border-#e0e0e0 px-5 py-2 border-rd-3px">复制</div>`
      // const language = hljs.getLanguage(lang) ? lang : 'plaintext'
      // return btnBox + hljs.highlight(code, { language }).value
      const language = hljs.getLanguage(lang) ? lang : 'plaintext'
      return hljs.highlight(code, { language }).value
    }
  })
)
const renderedHtml = computed(() => marked.parse(props.content, { gfm: true, breaks: true }))
// 获取回答的内容（剔除掉思考的内容）
const errorTips = '服务器繁忙，请稍后再试。'
const emptyTips = '暂无合适的内容，请稍后再试。'
const getAnswerContent = (content) => {
  if (content) {
    // 有内容时
    const reg = /<details[\s\S]*?<\/details>/g
    const match = content.match(reg)
    if (match) {
      return content.replace(match[0], '')
    }
    return content
  } else {
    if (props.status === 'error') {
      return errorTips
    }
    return emptyTips
  }
}
// 复制成功
const copySuccess = () => {
  proxy.$modal.msgSuccess('复制成功')
}
// 重新生成回答
const handleRefresh = (query, inputs) => {
  emit('on-refresh', query, inputs)
}

// 回显模型名
const showModelName = (model) => {
  return modelList?.find(item => item.value === model)?.label
}
</script>

<template>
  <div class="chat-question py-20 group">
    <div class="w-fit text-24px color-#1D2129 line-height-32px font-500 position-relative chat-self break-all">
      {{ query }}
      <div class="absolute right--0px top-66px hidden items-center group-hover:flex" translate="y--50%">
        <el-tooltip
          content="复制"
          :hide-after="0"
          :enterable="false"
          placement="top"
        >
          <div
            v-copyText="getAnswerContent(query)"
            v-copyText:callback="copySuccess"
            class="wh26 f-c-c border-rd-4px cursor-pointer hover:bg-#f3f3f3"
          >
            <img class="wh-20" :src="copy" alt="">
          </div>
        </el-tooltip>
      </div>
    </div>
    <!-- <div class="mt-4 text-12 line-height-20 color-#86909C">{{ parseTime(queryTime) }}</div> -->
  </div>
  <search-file v-if="files?.length" :model-value="userFiles" readonly />
  <div class="chat-answer">
    <div class="pt-16 flex items-center mb-4">
      <img class="mr-4px wh32" :src="settingStore.logoSrc" alt="">
      <div v-if="chatStore.inputs.ChatType === 'Chat'" class="border-rd-2px bg-#F2F3F5 px-8px line-height-24px text-12px font-500">{{ showModelName(inputs?.model) }}</div>
      <!-- <div class="border-rd-2px bg-#F2F3F5 px-8px line-height-24px text-12px font-500">{{ inputs?.model || 'DeepSeek' }}</div> -->
      <!-- <div class="border-rd-2px bg-#F2F3F5 px-8px line-height-24px text-12px font-500" style="margin-left:8px">{{ localName }}</div> -->
    </div>
    <!-- 没有内容展示 -->
    <div v-if="status === 'loading'" class="mt15 flex items-center">
      <el-icon class="is-loading mr-8" :size="24">
        <Loading />
      </el-icon>
      <div class="text-14px">生成中...</div>
    </div>
    <!-- 有内容展示 -->
    <template v-else>
      <div v-if="status === 'error'" class="mt15">
        <div>{{ errorTips }}</div>
      </div>
      <template v-else>
        <div v-if="renderedHtml" class="chat-md mt15 chat-box" v-html="renderedHtml" />
        <div v-else>{{ emptyTips }}</div>
      </template>
      <div v-if="status !== 'answering'" class="flex items-center justify-end">
        <el-tooltip
          content="复制"
          :hide-after="0"
          :enterable="false"
          placement="top"
        >
          <div
            v-copyText="getAnswerContent(content)"
            v-copyText:callback="copySuccess"
            class="wh26 f-c-c border-rd-4px cursor-pointer hover:bg-#f3f3f3"
          >
            <img class="wh-20" :src="copy" alt="">
          </div>
        </el-tooltip>
        <el-tooltip
          content="重新生成"
          :hide-after="0"
          :enterable="false"
          placement="top"
        >
          <div
            class="wh26 f-c-c border-rd-4px cursor-pointer hover:bg-#f3f3f3 ml-10"
            @click="handleRefresh(query, inputs)"
          >
            <img class="wh-20" :src="refresh" alt="">
          </div>
        </el-tooltip>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.chat-question{
  display: flex;
  justify-content: flex-end;
  .chat-self{
    background: #165DFF;
    border-radius: 12px 0px 12px 12px;
    padding: 12px;
    color: #fff;
    font-weight: 400;
    font-size: 16px;
    font-family: PingFang SC;
    line-height: 24px;
  }
}
.chat-answer{
  font-size: 16px;
  word-break: break-all;
  margin-bottom: 25px;
  ::v-deep(details){
    font-size: 14px;
    margin-bottom: 8px;
    summary{
      margin-bottom: 5px;
      cursor: pointer;
    }
  }
  .chat-md{
    ::v-deep(table){
      border-collapse: collapse;
      th, td{
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
      th{
        background-color: #f2f2f2;
      }
    }
  }
  ::v-deep(img) {
    max-width: 100%;
  }
}
:deep(.chat-box){
  line-height: 1.5;
  details{
    padding: 8px 16px !important;
    border-radius: 12px !important;
  }
}
</style>
