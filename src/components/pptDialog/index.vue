<template>
  <el-dialog
    v-model="dialogVisible"
    style="margin-top: 40px;"
    width="1200"
    :close-on-click-modal="false"
  >
    <div id="container" />
  </el-dialog>
</template>
<script setup>
import { useVModel } from '@vueuse/core'
import useChatStore from '@/store/modules/chat'
import { G } from '@/utils/docmee-ui-sdk-iframe.min.js'
import request from '@/utils/request'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  query: {
    type: String,
    default: ''
  }
})
const chatStore = useChatStore()
const dialogVisible = useVModel(props, 'modelValue', emit)
const emit = defineEmits(['update:modelValue'])
const docmeeUI = ref(null)
const showPpt = async() => {
  // 获取pptToken
  if (!chatStore.token) {
    await createApiToken()
  }
  nextTick(() => {
    console.log('TT', token.value)
    if (docmeeUI.value) {
      docmeeUI.value.destroy()
      docmeeUI.value = null
    }
    docmeeUI.value = new G({
      pptId: null,
      token: token.value || chatStore.token, // token
      container: document.querySelector('#container'), // 挂载 iframe 的容器
      page: 'creator', // 'creator' 创建页面; 'dashboard' PPT列表; 'customTemplate' 自定义模版; 'editor' 编辑页（需要传pptId字段）
      lang: 'zh', // 国际化
      mode: 'light', // light 亮色模式, dark 暗色模式
      isMobile: false, // 移动端模式
      themeColor: '#165DFF',
      hidePdfWatermark: true,
      // background: 'linear-gradient(-157deg,#f57bb0, #867dea)', // 自定义背景
      background: '#fff',
      creatorData: {
        text: props.query,
        createNow: true
      },
      padding: '40px 20px 0px',
      onMessage(message) {
        console.log('监听事件', message)
        if (message.type === 'invalid-token') {
        // 在token失效时触发
          console.log('token 认证错误')
        // 更换新的 token
        // let newToken = createApiToken()
        // docmeeUI.updateToken(newToken)
        } else if (message.type === 'mounted') {
          console.log('props.query ', props.query)
          setTimeout(() => {
            docmeeUI.value.changeCreatorData({ text: props.query }, true)
            chatStore.resetInputs()
          }, 500)
        } else if (message.type === 'beforeGenerate') {
          const { subtype, fields } = message.data
          if (subtype === 'outline') {
          // 生成大纲前触发
            console.log('即将生成ppt大纲', fields)
            return true
          } else if (subtype === 'ppt') {
          // 生成PPT前触发
            console.log('即将生成ppt', fields)
            docmeeUI.value.sendMessage({
              type: 'success',
              content: '继续生成PPT'
            })
            return true
          }
        } else if (message.type === 'beforeCreateCustomTemplate') {
          const { file, totalPptCount } = message.data
          // 是否允许用户继续制作PPT
          console.log('用户自定义完整模版，PPT文件：', file.name)
          if (totalPptCount < 2) {
            console.log('用户积分不足，不允许制作自定义完整模版')
            return false
          }
          return true
        } else if (message.type === 'pageChange') {
        //   pageChange(message.data.page)
        } else if (message.type === 'beforeDownload') {
        // 自定义下载PPT的文件名称
          const { subject } = message.data
          return `PPT_${subject}.pptx`
        } else if (message.type === 'error') {
          if (message.data.code === 88) {
          // 创建token传了limit参数可以限制使用次数
            alert('您的次数已用完')
          } else {
            alert('发生错误：' + message.data.message)
          }
        }
      }
    })
  })
}

// 获取pptToken
const token = ref('')
const createApiToken = async() => {
  const res = await request({
    url: '/cms/aippt/apiToken',
    method: 'GET'
  })
  token.value = res.data
  chatStore.setPPtToken(res.data)
}

watch(() => props.modelValue, (val) => {
  showPpt()
})

</script>
<style lang="scss" scoped>
#container {
  width: 100%;
  height: calc(100vh - 120px);
  margin: 0;
  padding: 0;
  border-radius: 12px;
  // box-shadow: 0 0 12px rgba(120, 120, 120, 0.3);
  overflow: hidden;
  /* background: linear-gradient(-157deg, #f57bb0, #867dea); */
  background: #fff;
  color: white;
}
</style>
