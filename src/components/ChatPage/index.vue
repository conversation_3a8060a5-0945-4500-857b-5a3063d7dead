<script setup name="ChatPage">
import ChatCom from '@/components/ChatCom'
import SearchCom from '@/components/SearchCom'
import request, { chatStream } from '@/utils/request'
import useChatStore from '@/store/modules/chat'
import useUserStore from '@/store/modules/user'
import { v4 as uuidv4 } from 'uuid'
import PptDialog from '@/components/pptDialog'

const props = defineProps({
  showNewChat: {
    type: Boolean,
    default: false
  },
  userId: {
    type: String,
    default: ''
  }
})
const iframeRef = ref()
const { proxy } = getCurrentInstance()
// 监听智能体新建对话
proxy.$emitter.on('agentNewChat', (type) => {
  if (type === 'DigitalPerson' && iframeRef.value) {
    const src = iframeRef.value.src
    iframeRef.value.src = src
  }
  chatStore.inputs.query = ''
  isAnswering.value = false // 智能体中新建对话时需要重置状态
  history.replaceState(null, '', `/agent/chat?type=${type}`)
  chatList.value = []
})
const uuidUser = localStorage.getItem('uuid') || uuidv4()
localStorage.setItem('uuid', uuidUser)

const route = useRoute()
const chatStore = useChatStore()
const current = ref({}) // 当前对话
const chatList = ref([]) // 对话列表
const newMessageId = ref('') // 最新一条消息id，若开始有历史消息，则为历史消息的最后一条id
const scrollWrapper = ref() // 滚动容器
const scrollContent = ref() // 滚动内容
const conversation_id = ref(route.query.id || '') // 当前对话id，默认浏览器地址参数，没有则为空
const isAnswering = ref(false) // 是否正在回答
const task_id = ref('') // 任务id
const user = computed(() => props.userId || useUserStore().userInfo.userId || uuidUser) // 优先使用传入的userId，其次是登录用户的userId，最后是本地uuid
// 获取历史消息
const getHistory = async(conversation_id) => {
  const url = '/dify/v1/messages'
  const params = {
    conversation_id,
    user: user.value
  }
  const res = await request({
    url,
    method: 'get',
    params
  })
  // 刚开始时将历史对话列表直接赋值给对话列表
  chatList.value = res.data.map(item => {
    // 如果item.answer里面有<think></think>标签，则将其替换为<details></details>标签
    if (item.answer.includes('<think>') && item.answer.includes('</think>')) {
      item.answer = item.answer.replace(/<think>/g, '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open><summary>深度思考...</summary>')
      item.answer = item.answer.replace(/<\/think>/g, '</details>')
    }
    return item
  })
  if (chatList.value.length) {
    newMessageId.value = chatList.value[chatList.value.length - 1].id
    // 从历史对话中设置聊天参数
    chatStore.inputs = {
      ...chatStore.inputs,
      ...chatList.value[chatList.value.length - 1].inputs
    }
  }
  nextTick(handleScrollBottom)
}
// 处理消息
const onMessage = (msg) => {
  if (msg.event === 'workflow_started') {
    // 工作流开始
    isAnswering.value = true
    chatStore.inputs.query = ''
    chatStore.chatFiles = []
    newMessageId.value = msg.message_id
    task_id.value = msg.task_id
    const inputs = msg.data.inputs
    // 如果没有对话id，则将对话id赋值给当前对话id，并将对话id写入浏览器地址栏
    if (!conversation_id.value) {
      conversation_id.value = msg.conversation_id
      if (route.path === '/chat') { // 只有是首页聊天时才跳转
        history.replaceState(null, '', `/chat?id=${msg.conversation_id}`)
      } else if (route.path === '/agent/chat') { // 智能体页面则在当前路由添加新参数id
        history.replaceState(null, '', `${location.href}&id=${msg.conversation_id}`)
      }
      // 首次对话设置消息name
      // request({
      //   url: `/dify/v1/conversations/${msg.conversation_id}/name`,
      //   method: 'post',
      //   data: {
      //     name: inputs['sys.query']?.slice(0, 30)?.trim(),
      //     user: user.value
      //   }
      // })
    }
    current.value = {
      inputs: inputs,
      query: inputs['sys.query'],
      created_at: msg.created_at,
      answer: '',
      status: 'loading'
    }
    // 如果有文件的话，还需要组装文件字段
    if (inputs['sys.files']?.length) {
      current.value.message_files = inputs['sys.files'].map(item => ({
        filename: item.filename,
        size: item.size,
        url: item.url
      }))
    }
    chatList.value.push(current.value)
    // 生成回答时滚动到最底部
    nextTick(handleScrollBottom)
  }
  if (msg.event === 'message') {
    current.value.status = 'answering'
    if (['Chat', 'OcrImg', 'IdentifyImg', 'CheckJob', 'Translate'].includes(chatStore.inputs.ChatType)) {
      // 该类型需要使用打字机效果
      if (count.value === 0) {
        current.value.answer += msg.answer
        count.value++
      } else {
        addStreamMessage(msg.answer)
      }
    } else {
      current.value.answer += msg.answer
    }
    // 生成回答时滚动到最底部
    nextTick(() => handleScrollBottom())
  }
  if (msg.event === 'error') {
    current.value.status = 'error'
    isAnswering.value = false
  }
  if (msg.event === 'message_end') {
    // 消息结束
    current.value.status = 'normal'
    isAnswering.value = false
  }
}
// 发起聊天
const handleChat = () => {
  const url = '/dify/v1/chat-messages'
  const params = {
    inputs: chatStore.inputs,
    query: chatStore.inputs.query,
    response_mode: 'streaming',
    conversation_id: conversation_id.value,
    user: user.value,
    files: chatStore.chatFiles
  }
  // 如果该会话里面有历史消息，则将历史消息最后一条的id赋值给parent_message_id传递给dify
  if (chatList.value.length) {
    params.parent_message_id = chatList.value[chatList.value.length - 1].id
  }
  chatStream(url, params, onMessage)
}

// ppt生成
const pptDialogVisible = ref(false)
const handlePpt = () => {
  pptDialogVisible.value = true
}

const queue = ref([]) // 缓存队列
const timer = ref(null)
const speed = ref(33.34) // 每个字符的速度
const count = ref(0)
// 添加流式信息并加入队列
function addStreamMessage(message) {
  queue.value.push(...message.split(''))

  if (!timer.value) {
    startTyping()
  }
}

// 启动打字机效果
function startTyping() {
  let char = ''
  if (queue.value.length === 0) {
    timer.value = null
    count.value = 0
    nextTick(handleScrollBottom)
    return
  }
  char = queue.value.shift()
  current.value.answer += char
  timer.value = setTimeout(startTyping, speed.value)
}

// 停止打字机
function stopTyping() {
  timer.value && clearTimeout(timer.value)
}

// 计算滚动元素和底部的距离
const getScrollBottom = () => {
  const currentEle = scrollContent.value
  const parentEle = scrollWrapper.value?.$el.querySelector('.el-scrollbar__wrap')
  const currentEleHeight = currentEle?.clientHeight
  const parentEleHeight = parentEle?.clientHeight
  const scrollTop = parentEle?.scrollTop
  return currentEleHeight - parentEleHeight - scrollTop
}

// 滚动到最底部事件（主要是在进入页面和生成回答时使用）
const handleScrollBottom = () => {
  if (isAnswering.value) {
    const bottom = getScrollBottom()
    // 当距离底部小于200时，才滚动到底部
    if (bottom < 200) {
      scrollWrapper.value?.setScrollTop(scrollContent.value?.clientHeight)
    }
  } else {
    scrollWrapper.value?.setScrollTop(scrollContent.value?.clientHeight)
  }
}
// 停止聊天
const handleStop = async() => {
  const url = `/dify/v1/chat-messages/${task_id.value}/stop`
  const params = {
    user: user.value
  }
  await request({
    url,
    method: 'post',
    data: params
  })
  isAnswering.value = false
}
// 重新生成回答（需要将问题带过来）
const handleRefresh = (msg, inputs) => {
  chatStore.inputs = {
    query: msg,
    ...inputs
  }
  handleChat()
}

defineExpose({
  getHistory
})

// TODO 非历史对话则表示新对话，需要携带参数
if (conversation_id.value) {
  getHistory(conversation_id.value)
} else if (chatStore.inputs.ChatType === 'Chat' && chatStore.inputs.query) {
  handleChat(chatStore.chatFiles, chatStore.inputs)
}

watch(() => route.query.id, val => {
  conversation_id.value = val
  if (val) {
    getHistory(val)
  } else {
    // 这里是在智能体页面新建对话时，需要清空对话列表
    chatList.value = []
  }
}, {
  immediate: true
})

// 清理定时器
onBeforeUnmount(() => {
  stopTyping()
})
</script>

<template>
  <div class="chat-page position-relative flex flex-col items-center h-full justify-center bg-[rgba(255,255,255,0.8)]">
    <template v-if="chatStore.currentAgent.chatType === 'DigitalPerson'">
      <iframe
        ref="iframeRef"
        :src="chatStore.currentAgent.externalUrl"
        class="wh-full"
        allow="microphone"
        frameborder="0"
      />
    </template>
    <template v-else>
      <el-scrollbar ref="scrollWrapper" class="w-full h-full mx-auto pt-50 overflow-auto flex-1">
        <div ref="scrollContent" class="max-w-900 mx-auto">
          <template v-if="chatList?.length">
            <chat-com
              v-for="item in chatList"
              :key="item.id"
              :query="item.query"
              :query-time="item.created_at"
              :inputs="item.inputs"
              :files="item.message_files"
              :content="item.answer"
              :status="item.status"
              @on-refresh="handleRefresh"
            />
          </template>
          <div v-else class="flex flex-col items-center pt25">
            <div class="text-32">{{ chatStore.currentAgent.title }}</div>
            <div class="text-18 mt-25">{{ chatStore.currentAgent.info }}</div>
          </div>
        </div>
      </el-scrollbar>
      <search-com
        :is-answering="isAnswering"
        :show-new-chat="showNewChat"
        :disable-file="!!conversation_id"
        class="mb-20 flex-0"
        @on-ppt="handlePpt"
        @on-chat="handleChat"
        @on-stop="handleStop"
      />
      <div class="color-#909399 text-12px line-height-20px f-c-c mt-[-20px]">内容由AI生成仅供参考</div>
    </template>
    <PptDialog v-model="pptDialogVisible" :query="chatStore.inputs.query" />
  </div>
</template>

<style lang="scss" scoped>
</style>
