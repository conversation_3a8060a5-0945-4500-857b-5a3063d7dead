<script setup name="ChatPage">
import ChatCom from '@/components/ChatCom'
import SearchCom from '@/components/SearchCom'
import request, { chatStream } from '@/utils/request'
import useChatStore from '@/store/modules/chat'
import useUserStore from '@/store/modules/user'
import { v4 as uuidv4 } from 'uuid'
import { nextTick } from 'vue'
import { getToken } from '@/utils/auth'
import { emitter } from '@/plugins'
import NProgress from 'nprogress'
import PptDialog from '@/components/PptDialog'
const uuidUser = localStorage.getItem('uuid') || uuidv4()
localStorage.setItem('uuid', uuidUser)

const route = useRoute()
const chatStore = useChatStore()
const query = ref('') // 用户搜索内容
const current = ref({}) // 当前对话
const chatList = ref([]) // 对话列表
const newMessageId = ref('') // 最新一条消息id，若开始有历史消息，则为历史消息的最后一条id
const scrollWrapper = ref() // 滚动容器
const scrollContent = ref() // 滚动内容
const conversation_id = ref(route.query.id || '') // 当前对话id，默认浏览器地址参数，没有则为空
const isAnswering = ref(false) // 是否正在回答
const task_id = ref('') // 任务id
const user = computed(() => useUserStore().userInfo.userId || uuidUser) // 没登录时uuid
const appId = ref('') // 文生appId
const queue = ref([]) // 缓存队列
const timer = ref(null)
const speed = ref(33.34) // 每个字符的速度
// 获取历史消息
const getHistory = async(conversation_id) => {
  const url = '/dify/v1/messages'
  const params = {
    conversation_id,
    user: user.value
  }
  const res = await request({
    url,
    method: 'get',
    params
  })
  // 刚开始时将历史对话列表直接赋值给对话列表
  chatList.value = res.data
  if (chatList.value.length) {
    newMessageId.value = chatList.value[chatList.value.length - 1].id
  }
  nextTick(handleScrollBottom)
}
const count = ref(0)
// 处理消息
const onMessage = (msg) => {
  if (msg.event === 'workflow_started') {
    // 工作流开始
    isAnswering.value = true
    query.value = ''
    newMessageId.value = msg.message_id
    task_id.value = msg.task_id
    const inputs = msg.data.inputs
    // 如果没有对话id，则将对话id赋值给当前对话id，并将对话id写入浏览器地址栏
    if (!conversation_id.value) {
      conversation_id.value = msg.conversation_id
      history.replaceState(null, '', `/chat?id=${msg.conversation_id}`)
      // 首次对话设置消息name
      request({
        url: `/dify/v1/conversations/${msg.conversation_id}/name`,
        method: 'post',
        data: {
          name: inputs['sys.query']?.slice(0, 30)?.trim(),
          user: user.value
        }
      })
    }
    current.value = {
      query: inputs['sys.query'],
      created_at: msg.created_at,
      answer: '',
      status: 'loading'
    }
    // 如果有文件的话，还需要组装文件字段
    if (inputs['sys.files']?.length) {
      current.value.message_files = inputs['sys.files'].map(item => ({
        filename: item.filename,
        size: item.size,
        url: item.url
      }))
    }
    chatList.value.push(current.value)
    // 生成回答时滚动到最底部
    nextTick(handleScrollBottom)
  }
  if (msg.event === 'message') {
    current.value.status = 'answering'
    if (count.value === 0) {
      current.value.answer += msg.answer
      count.value++
    } else {
      addStreamMessage(msg.answer)
    }
    // 生成回答时滚动到最底部
    handleScrollBottom()
  }
  if (msg.event === 'error') {
    current.value.status = 'error'
    isAnswering.value = false
  }
  if (msg.event === 'message_end') {
    // 消息结束
    current.value.status = 'normal'
    isAnswering.value = false
  }
}
// 发起聊天
const handleChat = (files, inputs) => {
  const url = '/dify/v1/chat-messages'
  const params = {
    inputs: inputs || {
      ChatType: 'chat',
      KnowledgeIds: window.localStorage.getItem('isKnowledge') || '',
      model: window.localStorage.getItem('model') || 'DeepSeek',
      Authorization: window.localStorage.getItem('Authorization') || '',
      is_internet: window.localStorage.getItem('is_internet') || '0'
    },
    query: query.value,
    response_mode: 'streaming',
    conversation_id: conversation_id.value,
    user: user.value,
    files
  }
  // 如果该会话里面有历史消息，则将历史消息最后一条的id赋值给parent_message_id传递给dify
  if (chatList.value.length) {
    params.parent_message_id = chatList.value[chatList.value.length - 1].id
  }
  chatStream(url, params, onMessage)
}

// 添加流式信息并加入队列
function addStreamMessage(message) {
  queue.value.push(...message.split(''))

  if (!timer.value) {
    startTyping()
  }
}

// 启动打字机效果
function startTyping() {
  let char = ''
  if (queue.value.length === 0) {
    timer.value = null
    count.value = 0
    nextTick(handleScrollBottom)
    return
  }
  char = queue.value.shift()
  current.value.answer += char
  timer.value = setTimeout(startTyping, speed.value)
}

// 停止打字机
function stopTyping() {
  clearTimeout(timer.value)
  timer.value = null
}
// 发起文生图
const handleImage = (params) => {
  getImage()
}

// 获取图片
const getImage = async() => {
  chatList.value.push({
    query: query.value,
    created_at: Date.now(),
    status: 'loading'
  })
  const res = await request({
    url: '/cms/xfyun/tti',
    method: 'POST',
    data: {
      header: {
        app_id: appId.value
      },
      parameter: {
        chat: {
          domain: 'general',
          width: 512,
          height: 512
        }
      },
      payload: {
        message: {
          text: [
            {
              role: 'user',
              content: query.value
            }
          ]
        }
      }
    }
  })
  chatList.value[chatList.value.length - 1] = {
    imageBase64: res.payload.choices.text[0].content,
    answer: res.payload.choices.text[0].polishContent,
    query: query.value,
    created_at: Date.now(),
    status: 'normal'
  }
  query.value = ''
  nextTick(handleScrollBottom)
}

// 发起文生PPT
const handlePPT = (params) => {
  console.log('发起文生PPT', params)
  if (getToken()) {
    pptDialogVisible.value = true
  } else {
    emitter.emit('showLoginDialog')
    NProgress.done()
  }
}

// PPT大纲生成
const pptTemplateObj = ref(null)

// 通过大纲生成PPT
const outlineToPPT = async() => {
  const time = Date.now()
  chatList.value[chatList.value.length - 1].generationStatus = 'loading'
  const res = await request({
    url: '/xfyun/ppt/api/ppt/v2/createPptByOutline',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      appId: appId.value,
      timestamp: timestamp.value,
      signature: signature.value
    },
    data: {
      query: query.value,
      outline: pptTemplateObj.value.outline,
      outlineSid: pptTemplateObj.value.sid,
      language: 'cn',
      author: 'AI助手XXXX',
      // templateId: '' // 直接供用户检索模板的ID,从PPT主题列表查询中获取；为空的话，从free模板中随机取一个
      aiImage: 'advanced', // ai配图类型： normal、advanced （isFigure为true的话生效）； normal-普通配图，20%正文配图；advanced-高级配图，50%正文配图
      isFigure: true // 是否自动配图
      // fileName: ''  // 文件名(带文件名后缀) ，传fileUrl的话必填
      // fileUrl:'', // 文件地址
      // isCardNote: true, // 是否生成PPT演讲备注
      // search: true // 是否联网搜索
    }
  })
  chatList.value[chatList.value.length - 1].generationStatus = 'normal'
  chatList.value[chatList.value.length - 1]['coverImgSrc'] = res.data.coverImgSrc
  chatList.value[chatList.value.length - 1]['pptStatus'] = 'building'
  nextTick(handleScrollBottom)
  console.log('通过大纲生成PPT', res)
  pptProgress(res.data, time)
}

// ppt进度查询
const pptResult = ref(null)
const pptProgress = async(data, time) => {
  const res = await request({
    url: '/xfyun/ppt/api/ppt/v2/progress?sid=' + data.sid,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      appId: appId.value,
      timestamp: timestamp.value,
      signature: signature.value
    }
  })
  if (res.data.pptStatus === 'building' || res.data.aiImageStatus === 'building' || res.data.cardNoteStatus === 'building') {
    setTimeout(() => {
      pptProgress(data, time)
    }, 3000)
  } else {
    pptResult.value = res.data
    chatList.value[chatList.value.length - 1]['pptStatus'] = 'done'
  }
  nextTick(handleScrollBottom)
  console.log('ppt进度查询', res)
}

// 计算滚动元素和底部的距离
const getScrollBottom = () => {
  const currentEle = scrollContent.value
  const parentEle = scrollWrapper.value.$el.querySelector('.el-scrollbar__wrap')
  const currentEleHeight = currentEle.clientHeight
  const parentEleHeight = parentEle.clientHeight
  const scrollTop = parentEle.scrollTop
  return currentEleHeight - parentEleHeight - scrollTop
}

// 下载PPT
const downloadPPT = () => {
  window.open(pptResult.value.pptUrl)
}
// PPT接口签名获取
const timestamp = ref('')
const signature = ref('')

// 滚动到最底部事件（主要是在进入页面和生成回答时使用）
const handleScrollBottom = () => {
  if (isAnswering.value) {
    const bottom = getScrollBottom()
    // 当距离底部小于200时，才滚动到底部
    if (bottom < 200) {
      scrollWrapper.value.setScrollTop(scrollContent.value.clientHeight)
    }
  } else {
    scrollWrapper.value.setScrollTop(scrollContent.value.clientHeight)
  }
}
// 停止聊天
const handleStop = async() => {
  const url = `/dify/v1/chat-messages/${task_id.value}/stop`
  const params = {
    user: user.value
  }
  await request({
    url,
    method: 'post',
    data: params
  })
  isAnswering.value = false
}
// 重新生成回答（需要将问题带过来）
const handleRefresh = (msg, inputs) => {
  query.value = msg
  const funcObj = {
    'chat': () => handleChat(null, inputs),
    'image': () => handleImage(),
    'ppt': () => handlePPT()
  }
  console.log('chatStore', chatStore.chatType)
  const chatType = chatStore.chatType || 'chat'
  funcObj[chatType]()
}

// ======================================== ppt生成（新）========================================
const pptDialogVisible = ref(false)
defineExpose({
  getHistory
})

if (conversation_id.value) {
  getHistory(conversation_id.value)
} else if (chatStore.chatParams && chatStore.chatType === 'chat') {
  query.value = chatStore.chatParams.query
  handleChat(chatStore.chatParams.files, null)
  chatStore.clearChatParams()
} else if (chatStore.imageParams && chatStore.chatType === 'image') {
  query.value = chatStore.imageParams.query
  handleImage(chatStore.imageParams)
  chatStore.clearImageParams()
} else if (chatStore.pptParams && chatStore.chatType === 'ppt') {
  query.value = chatStore.pptParams.query
  handlePPT(chatStore.pptParams)
  chatStore.clearPPTParams()
}

watch(() => route.query.id, val => {
  conversation_id.value = val
}, {
  immediate: true
})

// 清理定时器
onUnmounted(() => {
  stopTyping()
})
</script>

<template>
  <div class="chat-page position-relative flex flex-col h-full justify-center">
    <!-- <page-header /> -->
    <el-scrollbar ref="scrollWrapper" class="w-full h-full mx-auto pt-50 overflow-auto flex-1">
      <div ref="scrollContent" class="max-w-900 mx-auto">
        <chat-com
          v-for="item in chatList"
          :key="item.id"
          :query="item.query"
          :query-time="item.created_at"
          :inputs="item.inputs"
          :files="item.message_files"
          :content="item.answer"
          :status="item.status"
          :cover-img-src="item.coverImgSrc"
          :ppt-status="item.pptStatus"
          :image-base64="item.imageBase64"
          @on-refresh="handleRefresh"
          @generation-ppt="outlineToPPT"
          @download-ppt="downloadPPT"
        />
      </div>
    </el-scrollbar>
    <search-com
      v-model="query"
      :is-answering="isAnswering"
      show-new-chat
      disable-file
      :search-width="'900px'"
      class="w-full mb-20 flex-0"
      @on-chat="handleChat"
      @on-image="handleImage"
      @on-ppt="handlePPT"
      @on-stop="handleStop"
    />
    <div class="color-#C9CDD4 text-12px line-height-20px f-c-c mt-[-20px]">内容由AI生成仅供参考</div>
    <!-- <el-dialog
      v-model="pptDialogVisible"
      width="1200"
      style="margin-top:40px"
      :close-on-click-modal="false"
    >
      <div id="container" />
    </el-dialog> -->
    <PptDialog v-model="pptDialogVisible" :query="query" />
  </div>
</template>

<style lang="scss" scoped>
#container {
  width: 100%;
  height: calc(100vh - 120px);
  margin: 0;
  padding: 0;
  border-radius: 12px;
  // box-shadow: 0 0 12px rgba(120, 120, 120, 0.3);
  overflow: hidden;
  /* background: linear-gradient(-157deg, #f57bb0, #867dea); */
  background: #fff;
  color: white;
}
</style>
