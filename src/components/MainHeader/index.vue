<script setup name="index.vue">
defineProps({
  title: {
    type: String,
    default: ''
  },
  info: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <div class="page-header">
    <h1 class="page-title">{{ title }}</h1>
    <p class="page-desc">{{ info }}</p>
  </div>
</template>

<style lang="scss" scoped>
.page-header {

  .page-title {
    font-size: 32px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }

  .page-desc {
    font-size: 16px;
    color: #6b7280;
    border-bottom: 1px solid #D4D7DE;
    padding-bottom: 24px;
  }
}
</style>
