<template>
  <el-dialog
    :model-value="visible"
    title=""
    align-center
    style="padding:0px;"
    width="600px"
    header-class="login-header"
    :data-allow-mismatch="0"
    :close-on-click-modal="false"
    :show-close="true"
    @update:model-value="$emit('update:visible')"
    @close="handleClose"
  >
    <div class="login-dialog">
      <!-- 左侧图片区域 -->
      <div class="login-left">
        <img :src="settingStore.loginBgSrc" alt="登录背景">
      </div>

      <!-- 右侧登录表单区域 -->
      <div class="login-right">
        <el-form
          ref="loginRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
        >
          <div class="title">
            欢迎登录AI助手
            <!-- <div class="sub-title">坝立方平台</div> -->
          </div>
          <el-form-item prop="tel">
            <el-input
              v-model="loginForm.tel"
              text
              auto-complete="off"
              placeholder="请输入账号"
            >
              <template #prefix>
                <!-- <img src="@/assets/images/svg/icon_login_user.svg" alt=""> -->
                <img src="@/assets/images/svg/user.svg" width="18" height="18">
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="code">
            <el-input
              v-model="loginForm.code"
              text
              auto-complete="off"
              placeholder="验证码"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <!-- <svg-icon icon-class="validCode" class="el-input__icon input-icon" /> -->
                <img src="@/assets/images/svg/code.svg" width="18" height="18">
              </template>

              <template #suffix>
                <el-button
                  :loading="smsLoading"
                  text
                  @click="getSmsCode1"
                >
                  {{ smsDone ? `${smsTime}秒后重试` : smsLoading ? '验证码获取中' : '发送验证码' }}
                </el-button>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item style="width:100%;">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              style="width:100%;"
              @click.prevent="handleLogin"
            >
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { getSmsCode } from '@/api/login'
import useUserStore from '@/store/modules/user'
import useSettingStore from '@/store/modules/settings'
import { getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { getToken } from '@/utils/auth'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'login-success'])

const userStore = useUserStore()
const settingStore = useSettingStore()
const router = useRouter()
const { proxy } = getCurrentInstance()

const smsLoading = ref(false)
const smsDone = ref(false)
const smsTime = ref(120)
const loading = ref(false)

const loginForm = ref({
  tel: '',
  code: ''
})

const loginRules = {
  tel: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
}

const loginRef = ref(null)

// 处理登录
function handleLogin() {
  loginRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      userStore.LoginTel(loginForm.value).then(() => {
        userStore.getInfo().then(() => {
          loading.value = false
          userStore.getKnowlegeLib()
          emit('login-success', getToken())
          location.href = '/'
          handleClose()
        }).catch(() => {
          loading.value = false
        })
      }).catch(() => {
        loading.value = false
      })
    }
  })
}

// 获取验证码
function getSmsCode1() {
  if (!loginForm.value.tel) {
    proxy.$modal.msgError('请输入手机号')
    return
  }
  getSmsCode({
    phone: loginForm.value.tel
  }).then(() => {
    smsLoading.value = true
    setTimeout(() => {
      smsDone.value = true
      smsCount()
    }, 1000)
  })
}

function smsCount() {
  if (smsTime.value > 0) {
    setTimeout(() => {
      smsTime.value--
      smsCount()
    }, 1000)
  } else {
    smsTime.value = 120
    smsLoading.value = false
    smsDone.value = false
  }
}

function handleClose() {
  emit('update:visible', false)
  // 重置表单
  loginForm.value = {
    tel: '',
    code: ''
  }
}

</script>

<style lang="scss" scoped>
.login-dialog {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 400px;
  width: 600px;
}

.login-left {
  width: 240px;
  height: 400px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;

  overflow: hidden;

  img {
    width: 240px;
    height: 400px;
    object-fit: cover;
  }
}

.login-right {
  width: 343px;
  display: flex;
  padding: 24px;
  align-items: center;
  justify-content: center;
}

.title {
  margin: 0px auto 30px auto;
  font-size: 24px;
  line-height: 44px;
  font-weight: 600;
  color: rgba(0,0,0,.9);
  text-align: center;
}

.sub-title {
  font-size: 16px;
  color: #707070;
  font-weight: normal;
}

:deep(.el-dialog__headerbtn) {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;

  .el-dialog__close {
    color: #fff;
    font-size: 20px;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.login-form {
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
}

:deep(.el-dialog__header){
  padding-bottom: 0px;
}
.el-form-item {
  margin-bottom: 20px;
}
:deep(.el-input__wrapper){
  padding: 1px 4px 1px 11px !important;
}
</style>
