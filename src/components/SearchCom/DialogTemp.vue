<script setup name="DialogTemp">
import { useVModel } from '@vueuse/core'
import useChatStore from '@/store/modules/chat'
import { onClickOutside } from '@vueuse/core'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue'])
const visible = useVModel(props, 'modelValue', emit)
const chatStore = useChatStore()
const dialogRef = ref()

onClickOutside(dialogRef, (event) => {
  // 检查点击的元素是否在 "模板" 按钮内部
  if (event.target.closest('.template-button')) {
    return
  }
  visible.value = false
})
const tempList = computed(() => chatStore.currentAgent?.tempList)

// 模板点击事件
const hanldeTempClick = (item) => {
  chatStore.inputs.query = item.content
  visible.value = false
}
</script>

<template>
  <Transition>
    <div
      v-if="visible"
      ref="dialogRef"
      class="absolute w-100% max-h-400 bottom-100% bg-#fff border-rd-16 py-16 select-none"
      shadow="[0px_12px_32px_4px_rgba(0,0,0,0.04),0px_0px_20px_0px_rgba(0,0,0,0.08)]"
    >
      <div class="h-full px-16 flex flex-wrap overflow-y-auto">
        <div
          v-for="item, index in tempList"
          :key="index"
          class="w-205 p-10 border-rd-8 border-1px border-solid border-#D4D7DE mt-16 mr-16 [&:nth-child(4n)]:mr-0 [&:nth-child(1)]:mt-0 [&:nth-child(2)]:mt-0 [&:nth-child(3)]:mt-0 [&:nth-child(4)]:mt-0 cursor-pointer"
          @click="hanldeTempClick(item)"
        >
          <div class="text-16 font-500 line-height-24 color-#303133">{{ item.title }}</div>
          <div class="text-14 line-height-22 color-#909399">{{ item.info }}</div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
