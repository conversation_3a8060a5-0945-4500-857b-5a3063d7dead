<script setup name="SearchCom">
import { useVModel } from '@vueuse/core'
import file from '@/assets/images/svg/icon_file.svg'
import request from '@/utils/request'
import SearchFile from '@/components/SearchFile'
import useUserStore from '@/store/modules/user'
import { getToken } from '@/utils/auth'
import useChatStore from '@/store/modules/chat'
import inter from '@/assets/images/svg/inter.svg'
import noInter from '@/assets/images/svg/no-inter.svg'
const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const router = useRouter()
const chatStore = useChatStore()
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  showNewChat: {
    // 显示“新建对话”按钮（在对话中启用）
    type: Boolean,
    default: false
  },
  // disableModel: { // 禁用大模型选择（在对话中启用）
  //   type: Boolean,
  //   default: false
  // },
  disableFile: {
    // 禁用大模型选择（在对话中启用）
    type: Boolean,
    default: false
  },
  isAnswering: {
    // 是否正在回答问题
    type: Boolean,
    default: false
  },
  searchWidth: {
    type: String,
    default: '960px'
  }
})
const emit = defineEmits([
  'on-chat',
  'on-image',
  'on-ppt',
  'on-stop',
  'update:modelValue'
])
const query = useVModel(props, 'modelValue', emit)

const buttonRef = ref()
const chooseFiles = ref([])
const user = computed(() => userStore.userInfo.userId)

// 获取文件类型，为dify做数据处理
// document 具体类型包含：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
// image 具体类型包含：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
// audio 具体类型包含：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
// video 具体类型包含：'MP4', 'MOV', 'MPEG', 'MPGA'
// custom 具体类型包含：其他文件类型
const getFileType = (name) => {
  const ext = name.split('.').pop().toLowerCase()
  const document = ['txt', 'md', 'markdown', 'pdf', 'html', 'xlsx', 'xls', 'docx', 'doc', 'csv', 'eml', 'msg', 'pptx', 'ppt', 'xml', 'epub']
  const image = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
  const audio = ['mp3', 'm4a', 'wav', 'webm', 'amr']
  const video = ['mp4', 'mov', 'mpeg', 'mpga']
  if (image.includes(ext)) {
    return 'image'
  } else if (audio.includes(ext)) {
    return 'audio'
  } else if (video.includes(ext)) {
    return 'video'
  } else if (document.includes(ext)) {
    return 'document'
  } else {
    return 'custom'
  }
}
// 组装文件对象（前提是所有文件已经上传完毕：status不为loading或error），
// transfer_method根据是否有id来判断，有id则为local_file，否则为remote_url
const getFiles = (files) => {
  console.log(`output->files`, files)
  return files.map((item) => {
    const params = {
      type: getFileType(item.name),
      transfer_method: item.id ? 'local_file' : 'remote_url'
    }
    if (item.id) {
      params.upload_file_id = item.id
    } else {
      // TODO: 这里需要根据知识库的url来获取文件的url
      params.url = item.url
    }
    return params
  })
}
// 开始聊天
const handleChat = () => {
  if (query.value.trim() === '') {
    proxy.$modal.msgError('请输入您的问题')
    return
  }
  // 这里来判断文件是否全部上传成功，根据chooseFiles的status来判断
  if (chooseFiles.value.length === 0) {
    emit('on-chat')
  } else {
    if (chooseFiles.value.some((item) => item.status === 'loading')) {
      proxy.$modal.msgError('文件正在上传中，请稍后')
    } else if (chooseFiles.value.some((item) => item.status === 'error')) {
      proxy.$modal.msgError('文件上传失败，请重新上传')
    } else {
      // 传递出去后，则清空chooseFiles
      emit('on-chat', getFiles(chooseFiles.value))
      chooseFiles.value = []
    }
  }
}

// 开始文生图
const handleImage = () => {
  const params = {}
  emit('on-image', params)
}

// 开始文生PPT
const handlePPT = () => {
  const params = {}
  emit('on-ppt', params)
}

// 停止聊天
const handleStop = () => {
  emit('on-stop')
}
// 点击回车键
const handleEnter = () => {
  if (props.isAnswering) return
  if (chatType.value === 'chat') {
    handleChat()
  } else if (chatType.value === 'image') {
    handleImage()
  } else if (chatType.value === 'ppt') {
    handlePPT()
  }
}
// 新建对话
const handleNewChat = () => {
  router.push('/')
}

// 选择上传文件
const handleUpload = () => {
  // eslint-disable-next-line eqeqeq
  if (is_internet.value == '1' || props.disableFile) return
  // Create a hidden file input element
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.multiple = true
  fileInput.accept = '.pdf,.doc,.docx,.xls,.xlsx'
  fileInput.style.display = 'none'
  document.body.appendChild(fileInput)

  // Trigger file selection
  fileInput.click()

  // Handle file selection
  fileInput.onchange = async(e) => {
    const files = Array.from(e.target.files)
    if (files.length + chooseFiles.value.length > 10) {
      proxy.$modal.msgError('最多支持上传10个文件')
      return
    }
    for (const file of files) {
      const chooseFile = {
        // 给chooseFiles列表添加的文件
        timestamp: Date.now(), // 将chooseFile push进chooseFiles.value时，他们不再相等，所以需要一个timestamp来标识
        name: file.name,
        size: file.size,
        status: 'loading'
      }
      chooseFiles.value.push(chooseFile)
      const formData = new FormData()
      formData.append('file', file)
      formData.append('user', user.value)
      try {
        const response = await request({
          url: '/dify/v1/files/upload',
          method: 'post',
          data: formData
        })
        if (response.id) {
          // 表示上传成功
          const index = chooseFiles.value.findIndex(
            (item) => item.timestamp === chooseFile.timestamp
          )
          if (index !== -1) {
            chooseFiles.value[index] = {
              ...chooseFile,
              id: response.id,
              status: 'success'
            }
          }
        } else {
          chooseFile.status = 'error'
        }
      } catch (error) {
        console.error('Upload failed:', error)
      }
    }

    // Clean up
    document.body.removeChild(fileInput)
  }
}

// 选择模型
const modelList = ref([
  {
    value: 'DeepSeek',
    label: 'DeepSeek'
  },
  {
    value: 'QwQ',
    label: '通义千问'
  }
])
const model = ref(localStorage.getItem('model') || 'DeepSeek')
const handleModelChange = (val) => {
  localStorage.setItem('model', val)
}
// 选择知识库
const knowledgeList = computed(() => userStore.knowledgeLibs)
const getLocalKnowledge = () => {
  const localKnowlege = localStorage.getItem('isKnowledge')
  if (localKnowlege?.length) {
    return localKnowlege.split(',')
  } else {
    return []
  }
}
const isKnowledge = ref(getLocalKnowledge())
const handleKnowledgeChange = (val) => {
  localStorage.setItem('isKnowledge', val)
}
const isLogin = ref(!!getToken())
proxy.$emitter.on('refreshToken', () => {
  console.log(`output->5555555`, 5555555)
  isLogin.value = !!getToken()
})
const handleKnowledgeClick = () => {
  if (!isLogin.value) {
    proxy.$emitter.emit('showLoginDialog')
  }
}
// 选择联网
const is_internet = ref(localStorage.getItem('is_internet') || '0')
const handleNetClick = () => {
  if (props.disableFile) return
  // 当选择了知识库或上传了文件不允许联调
  if (isKnowledge.value?.length || chooseFiles.value?.length) {
    return proxy.$modal.msgError('请先删除文件和知识库后再联网')
  }
  is_internet.value = Math.abs(is_internet.value - 1)
  localStorage.setItem('is_internet', is_internet.value)
}
// ============================文生图/视频/PPT=============================
const funcModules = [
  {
    icon: new URL(`@/assets/images/svg/func-modules-1.svg`, import.meta.url)
      .href,
    text: '文生图',
    key: 'image',
    disabled: false,
    onClick: (type) => {
      if (!isLogin.value) {
        proxy.$emitter.emit('showLoginDialog')
        return
      }
      chatStore.setChatType(type)
    }
  },
  {
    icon: new URL(`@/assets/images/svg/func-modules-3.svg`, import.meta.url)
      .href,
    disabledIcon: new URL(
      `@/assets/images/svg/func-modules-dis-3.svg`,
      import.meta.url
    ).href,
    text: '文生PPT',
    key: 'ppt',
    disabled: false,
    onClick: (type) => {
      if (!isLogin.value) {
        proxy.$emitter.emit('showLoginDialog')
        return
      }
      chatStore.setChatType(type)
    }
  },
  {
    icon: new URL(`@/assets/images/svg/func-modules-2.svg`, import.meta.url)
      .href,
    disabledIcon: new URL(
      `@/assets/images/svg/func-modules-dis-2.svg`,
      import.meta.url
    ).href,
    text: '文生视频',
    key: 'video',
    disabled: true,
    onClick: (type) => {
      chatStore.setChatType(type)
    }
  }
]

const chatType = computed(() => chatStore.chatType) // chat image video ppt
const chatTypeMap = ref({
  image: '文生图',
  video: '数字人(开发中)',
  ppt: '文生PPT'
})

// 关闭多功能展示板
const handleCloseFunc = () => {
  chatStore.setChatType('chat')
}

</script>

<template>
  <div class="search-com">
    <el-button
      v-if="showNewChat"
      type="primary"
      size="large"
      plain
      class="flex h38 mx-auto border-rd-8px border-[--el-color-primary-light-4] px-12 mb15 add-chat bg-#fff"
      shadow="[0px_4px_10px_0px_rgba(0,0,0,0.10)]"
      @click="handleNewChat"
    >
      <svg-icon icon-class="chat" class="mr8 wh18" />
      新建对话</el-button>
    <!-- 多功能模块 -->
    <div
      v-show="chatType === 'chat'"
      class="func-modules-box mx-auto mb-15 w-fit"
    >
      <div
        v-for="(item, index) in funcModules"
        :key="index"
        class="func-modules-item"
        @click="item.onClick(item.key)"
      >
        <el-button :disabled="item.disabled">
          <template #icon>
            <img
              style="width: 20px"
              :src="item.disabled ? item.disabledIcon : item.icon"
              alt=""
            >
          </template>
          {{ chatTypeMap[item.key] }}
        </el-button>
      </div>
    </div>

    <div
      :style="{ width: searchWidth }"
      class="mt-10 mx-auto border-2px border-solid border-[--el-color-primary-light-4] bg-#FAFAFA border-rd-16px overflow-hidden px-12px"
    >
      <search-file v-if="chooseFiles?.length" v-model="chooseFiles" />
      <div v-show="chatType !== 'chat'" class="func-box">
        <div class="func-box-title">
          <div class="text-14px font-600 func-text">
            <img src="@/assets/images/svg/textToImg.svg" width="18" height="18">
            <span>{{ chatTypeMap[chatType] }}</span>
          </div>
          <el-icon class="cursor-pointer" @click="handleCloseFunc"><Close /></el-icon>
        </div>
      </div>
      <el-input
        v-model="query"
        class="h-64"
        :placeholder="'问问AI助手'"
        @keyup.enter="handleEnter"
      />
      <div class="flex justify-between items-center py-10px">
        <div>
          <el-select
            v-model="model"
            :disabled="!isLogin || disableFile"
            class="w140 model"
            @change="handleModelChange"
            @click="handleKnowledgeClick"
          >
            <el-option
              v-for="item in modelList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
              :disabled="item.disabled"
            />
          </el-select>
          <el-select
            v-if="chatType === 'chat'"
            v-model="isKnowledge"
            placeholder="深度思考"
            :disabled="!isLogin || is_internet == '1' || disableFile"
            multiple
            clearable
            collapse-tags
            class="w180 ml10 knowledge"
            :class="{knowledgeActive: isKnowledge.length > 0}"
            @change="handleKnowledgeChange"
            @click="handleKnowledgeClick"
          >
            <el-option-group>
              <el-option-group
                v-for="item in knowledgeList"
                :key="item.value"
                :label="item.label"
              >
                <el-option
                  v-for="ele in item.group"
                  :key="ele.id"
                  :value="ele.id"
                  :label="ele.name"
                />
              </el-option-group>
            </el-option-group>
          </el-select>
          <el-tag
            v-if="chatType === 'chat'"
            :type="is_internet == '1' ? 'primary' : 'info'"
            class="ml10 cursor-pointer select-none text-14"
            disable-transitions
            :disabled="disableFile"
            size="large"
            @click="handleNetClick"
          >
            <div style="display: flex; align-items: center;">
              <img :src="is_internet == '1' ? inter : noInter">
              <span style="color:#303133;margin-left:4px" :style="{color: is_internet == '1' ? '#165DFF' :'#303133'}">{{ is_internet == "1" ? "已联网" : "未联网" }}</span>
            </div>

          </el-tag>
        </div>
        <div class="f-c-c">
          <el-tooltip
            placement="top"
            :hide-after="0"
            :enterable="false"
            effect="dark"
          >
            <template #content>
              <div v-if="disableFile" class="font-12 color-white font-bold">
                会话中不支持上传文件
              </div>
              <div
                v-else-if="is_internet == '1'"
                class="font-12 color-white font-bold"
              >
                联网搜索不支持上传文件
              </div>
              <template v-else>
                <div class="font-12 color-white font-bold">
                  文件数量：最多支持10个
                </div>
                <div class="font-12 color-white font-bold">
                  支持PDF、DOC、XLS
                </div>
              </template>
            </template>
            <img
              v-if="chatType === 'chat'"
              ref="buttonRef"
              :class="[
                'cursor-pointer w24 h24',
                is_internet == '1' || disableFile ? 'opacity-50' : '',
              ]"
              :src="file"
              @click="handleUpload"
            >
          </el-tooltip>
          <svg-icon
            v-if="isAnswering"
            icon-class="stop"
            class="ml10 wh24 cursor-pointer color-[--el-color-primary]"
            @click="handleStop"
          />
          <svg-icon
            v-else
            icon-class="send"
            class="ml10 wh24 cursor-pointer color-[--el-color-primary]"
            @click="handleEnter"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.search-com {
  position: relative;
  .add-chat{
    position: absolute;
    left: 50%;
    top: -50px;
    transform: translateX(-50%);
  }
  ::v-deep(.el-input__wrapper) {
    background-color: transparent;
    box-shadow: none;
    padding-left: 0;
    padding-right: 0;
    .el-input__inner {
      color: #1d2129;
    }
  }
  .func-modules-box {
    display: flex;
    gap: 0 24px;
    .func-modules-item {
      cursor: pointer;
      // padding: 4px 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0 8px;
      height: 36px;
      // border: 1px solid #c8cdd3;
      // border-radius: 8px;
      .func-modules-item-icon {
        display: flex;
        align-items: center;
        img {
          width: 20px;
          height: 20px;
        }
      }
      .func-modules-item-text {
        font-size: 14px;
        color: #1d2129;
      }
    }
  }
}
.func-box {
  margin-top: 10px;
  .func-box-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .func-text{
      width: 86px;
      height: 32px;
      background: #165DFF;
      text-align: center;
      line-height: 32px;
      color: #fff;
      font-weight: 500;
      font-family: PingFang SC;
      display: flex;
      align-items: center;
      justify-content: center;
      gap:0 4px;
      border-radius: 8px;
    }
  }
}
::v-deep(.el-select__wrapper){
  background-color: #f0f2f5;
  box-shadow: none;
  border-radius: 8px;
}
.model {
    ::v-deep(.el-select__wrapper) {
      background-color: #E8F3FF;
      .el-select__selection {
        .el-select__selected-item {
          color: #165dff;
        }
      }
      .el-select__suffix{
        .el-select__icon{
          color: #165dff;
        }
      }
    }

}
.knowledge {
  ::v-deep(.el-select__selected-item){
    .el-tag{
      background: rgba(255,255,255,0.6) !important;
      color: #165DFF;
      .el-tag__close{
        color: #165DFF !important;
      }
    }
  }
}
.knowledgeActive{
  ::v-deep(.el-select__wrapper) {
    background-color: #E8F3FF;
    .el-select__suffix{
      .el-select__icon{
        color: #165dff;
      }
    }
  }
}
.model,.knowledge{
  ::v-deep(.is-hovering){
    box-shadow: 0 0 0 1px #7ea3f9 !important;
  }
}

.el-tag{
  border: none;
  border-radius: 8px;
}
</style>
