<script setup name="SearchSkill">
import useChatStore from '@/store/modules/chat'

defineProps({
  disableFile: {
    type: Boolean,
    default: false
  }
})
const chatStore = useChatStore()
</script>

<template>
  <div>
    <el-select
      v-for="skill in chatStore.currentAgent.skills"
      :key="skill.prop"
      v-model="chatStore.inputs[skill.prop]"
      class="w140 model"
    >
      <el-option
        v-for="item in skill.options"
        :key="item.value"
        :value="item.value"
        :label="item.label"
        :disabled="item.disabled"
      />
    </el-select>
  </div>
</template>

<style lang="scss" scoped>
</style>
