<script setup name="SearchCom">
import file from '@/assets/images/svg/icon_file.svg'
import request from '@/utils/request'
import SearchFile from '@/components/SearchFile'
import useUserStore from '@/store/modules/user'
import { getToken } from '@/utils/auth'
import useChatStore from '@/store/modules/chat'
import inter from '@/assets/images/svg/inter.svg'
import noInter from '@/assets/images/svg/no-inter.svg'
import { deepClone } from '@/utils'
import SearchSkill from './SearchSkill.vue'
import DialogTemp from './DialogTemp.vue'

const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const router = useRouter()
const chatStore = useChatStore()
const tempVisible = ref(false)

// 组件挂载时获取知识库列表
onMounted(() => {
  if (getToken()) {
    userStore.getKnowlegeLib()
  }
})

const props = defineProps({
  showNewChat: {
    // 显示"新建对话"按钮（在对话中启用）
    type: Boolean,
    default: false
  },
  // disableModel: { // 禁用大模型选择（在对话中启用）
  //   type: Boolean,
  //   default: false
  // },
  disableFile: {
    // 禁用大模型选择（在对话中启用）
    type: Boolean,
    default: false
  },
  isAnswering: {
    // 是否正在回答问题
    type: Boolean,
    default: false
  },
  searchWidth: {
    type: String,
    default: '900px'
  }
})
const emit = defineEmits([
  'on-chat',
  'on-stop',
  'on-ppt'
])

const buttonRef = ref()
const chooseFiles = ref([])
const user = computed(() => userStore.userInfo.userId)

// 获取文件类型，为dify做数据处理
// document 具体类型包含：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
// image 具体类型包含：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
// audio 具体类型包含：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
// video 具体类型包含：'MP4', 'MOV', 'MPEG', 'MPGA'
// custom 具体类型包含：其他文件类型
const getFileType = (name) => {
  const ext = name.split('.').pop().toLowerCase()
  const document = ['txt', 'md', 'markdown', 'pdf', 'html', 'xlsx', 'xls', 'docx', 'doc', 'csv', 'eml', 'msg', 'pptx', 'ppt', 'xml', 'epub']
  const image = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
  const audio = ['mp3', 'm4a', 'wav', 'webm', 'amr']
  const video = ['mp4', 'mov', 'mpeg', 'mpga']
  if (image.includes(ext)) {
    return 'image'
  } else if (audio.includes(ext)) {
    return 'audio'
  } else if (video.includes(ext)) {
    return 'video'
  } else if (document.includes(ext)) {
    return 'document'
  } else {
    return 'custom'
  }
}
// 组装文件对象（前提是所有文件已经上传完毕：status不为loading或error），
// transfer_method根据是否有id来判断，有id则为local_file，否则为remote_url
const getFiles = (files) => {
  console.log(`output->files`, files)
  return files.map((item) => {
    const params = {
      type: getFileType(item.name),
      transfer_method: item.id ? 'local_file' : 'remote_url'
    }
    if (item.id) {
      params.upload_file_id = item.id
    } else {
      // TODO: 这里需要根据知识库的url来获取文件的url
      params.url = item.url
    }
    return params
  })
}
// 开始聊天
const handleChat = () => {
  if (chatStore.inputs.query.trim() === '') {
    proxy.$modal.msgError('请输入您的问题')
    return
  }
  // 这里来判断文件是否全部上传成功，根据chooseFiles的status来判断
  if (chooseFiles.value.length === 0) {
    emit('on-chat')
  } else {
    if (chooseFiles.value.some((item) => item.status === 'loading')) {
      proxy.$modal.msgError('文件正在上传中，请稍后')
    } else if (chooseFiles.value.some((item) => item.status === 'error')) {
      proxy.$modal.msgError('文件上传失败，请重新上传')
    } else {
      if (['Img2Video', 'DataAnalysis'].includes(chatStore.inputs.ChatType)) {
        chatStore.inputs.FileUpload = chooseFiles.value.map(item => item.url).join(',')
        emit('on-chat')
        chooseFiles.value = []
      } else {
        // 传递出去后，则清空chooseFiles
        chatStore.chatFiles = getFiles(deepClone(chooseFiles.value))
        emit('on-chat')
        chooseFiles.value = []
      }
    }
  }
}

// 停止聊天
const handleStop = () => {
  emit('on-stop')
}
// 点击回车键
const handleEnter = () => {
  if (props.isAnswering) return
  if (chatStore.inputs.ChatType === 'Text2PPT') {
    handlePPT()
  } else {
    handleChat()
  }
}

// 处理键盘事件
const handleKeyDown = (e) => {
  if (e.shiftKey) {
    // Shift+回车正常换行，不阻止默认行为
    return
  }
  // 普通回车，阻止默认换行行为，触发事件
  e.preventDefault()
  handleEnter()
}

// 生成ppt
const handlePPT = () => {
  emit('on-ppt')
}
// 新建对话
const handleNewChat = () => {
  router.push('/')
}

// 选择上传文件
const handleUpload = () => {
  if (!isLogin.value) {
    proxy.$emitter.emit('showLoginDialog')
    return
  }
  // eslint-disable-next-line eqeqeq
  if (chatStore.inputs.is_internet == '1') return
  // Create a hidden file input element
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.multiple = true
  // fileInput.accept = '.pdf,.doc,.docx,.xls,.xlsx,.png,.jpeg,.jpg'
  fileInput.accept = chatStore.currentAgent?.fileType
  fileInput.style.display = 'none'
  document.body.appendChild(fileInput)

  // Trigger file selection
  fileInput.click()

  // Handle file selection
  fileInput.onchange = async(e) => {
    const files = Array.from(e.target.files)
    if (files.length + chooseFiles.value.length > chatStore.currentAgent.fileLimit) {
      proxy.$modal.msgError(`最多支持上传${chatStore.currentAgent.fileLimit}个文件`)
      return
    }
    // 定义一个index，用来标识当前上传的文件
    let index = chooseFiles.value?.length || 0
    files.forEach(async(file) => {
      if (file.size > 20 * 1024 * 1024) {
        proxy.$modal.msgError(`文件大小不能超过20MB`)
        return
      }
      const chooseFile = {
        // 给chooseFiles列表添加的文件
        // timestamp: Date.now(), // 将chooseFile push进chooseFiles.value时，他们不再相等，所以需要一个timestamp来标识
        index: index++,
        name: file.name,
        size: file.size,
        status: 'loading'
      }
      chooseFiles.value.push(chooseFile)
      const formData = new FormData()
      formData.append('file', file)
      formData.append('user', user.value)
      try {
        if (['Img2Video', 'DataAnalysis'].includes(chatStore.inputs.ChatType)) { // 图生视频、数据分析需要单独处理上传
          const response = await request({
            url: '/cms/common/upload',
            method: 'post',
            data: formData
          })
          if (response.code === 200) {
            // 表示上传成功
            const index = chooseFiles.value.findIndex(
              (item) => item.index === chooseFile.index
            )
            delete chooseFile.index // 这里要删除掉这个属性，不然在上传后又删除文件再上传时会有重复的
            if (index !== -1) {
              chooseFiles.value[index] = {
                name: response.originalFilename,
                size: chooseFiles.value[index].size,
                url: response.url,
                status: 'success'
              }
            }
          }
        } else {
          const response = await request({
            url: '/dify/v1/files/upload',
            method: 'post',
            data: formData
          })
          if (response.id) {
            // 表示上传成功
            const index = chooseFiles.value.findIndex(
              (item) => item.index === chooseFile.index
            )
            delete chooseFile.index // 这里要删除掉这个属性，不然在上传后又删除文件再上传时会有重复的
            if (index !== -1) {
              chooseFiles.value[index] = {
                ...chooseFile,
                id: response.id,
                status: 'success'
              }
            }
          } else {
            chooseFile.status = 'error'
          }
        }
      } catch (error) {
        console.error('Upload failed:', error)
      }
    })

    // Clean up
    document.body.removeChild(fileInput)
  }
}

// 选择知识库
const knowledgeList = computed(() => userStore.knowledgeLibs)
const isKnowledge = computed({
  get() {
    const KnowledgeIds = chatStore.inputs.KnowledgeIds
    if (KnowledgeIds?.length) {
      return chatStore.inputs.KnowledgeIds?.split(',')
    }
    return []
  },
  set(val) {
    chatStore.inputs.KnowledgeIds = val?.join(',')
  }
})
const isLogin = ref(!!getToken())
proxy.$emitter.on('refreshToken', () => {
  isLogin.value = !!getToken()
  if (isLogin.value) {
    userStore.getKnowlegeLib()
  }
})
const handleKnowledgeClick = () => {
  if (!isLogin.value) {
    proxy.$emitter.emit('showLoginDialog')
  }
}
// 选择联网
const handleNetClick = () => {
  // if (props.disableFile) return
  // 当选择了知识库或上传了文件不允许联调
  if (isKnowledge.value?.length || chooseFiles.value?.length) {
    return proxy.$modal.msgError('请先删除文件和知识库后再联网')
  }
  chatStore.inputs.is_internet = Math.abs(chatStore.inputs.is_internet - 1)
}

const handle = () => {
  tempVisible.value = !tempVisible.value
}
</script>

<template>
  <div class="search-com">
    <el-button
      v-if="showNewChat"
      type="primary"
      size="large"
      plain
      class="flex h38 mx-auto border-rd-8px border-[--el-color-primary-light-4] px-12 mb15 add-chat bg-#fff"
      shadow="[0px_4px_10px_0px_rgba(0,0,0,0.10)]"
      @click="handleNewChat"
    >
      <svg-icon icon-class="chat" class="mr8 wh18" />
      新建对话</el-button>
    <div
      :style="{ width: searchWidth }"
      class="mt-10 mx-auto border-2px border-solid border-[--el-color-primary-light-4] bg-#fff border-rd-16px overflow-hidden px-12px"
    >
      <div class="clearfix">
        <div v-if="chatStore.currentAgent.title" class="float-left bg-#165DFF border-rd-8 px-8 py-4 color-#fff text-16 line-height-24 font-500 mt-16">
          <svg-icon :icon-class="chatStore.currentAgent.icon" class="mr-4" />{{ chatStore.currentAgent.title }}
        </div>
        <div v-if="chatStore.currentAgent.tempList?.length" class="template-button float-right bg-#E6E8EB border-rd-8 px-8 py-4 color-#606266 text-16 line-height-24 font-500 mt-16 cursor-pointer" @click.stop="handle">
          <svg-icon :icon-class="tempVisible ? 'fold' : 'un_fold'" class="mr-4" />
          模板
        </div>
      </div>
      <search-file v-if="chooseFiles?.length" v-model="chooseFiles" />
      <el-input
        v-model="chatStore.inputs.query"
        type="textarea"
        :autosize="{ minRows: 3, maxRows: 8 }"
        maxlength="1000"
        show-word-limit
        class="search-textarea mt-16"
        :placeholder="'问问AI助手'"
        @keydown.enter="handleKeyDown"
      />
      <div class="flex justify-between items-center py-10px">
        <div v-if="['Chat', 'Text2OfficeFile'].includes(chatStore.inputs.ChatType)">
          <el-select
            v-model="chatStore.inputs.model"
            :disabled="!isLogin"
            class="w140 model"
            @click="handleKnowledgeClick"
          >
            <el-option
              v-for="item in chatStore.modelList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
              :disabled="item.disabled"
            />
          </el-select>
          <el-select
            v-model="isKnowledge"
            placeholder="深度思考"
            :disabled="!isLogin || chatStore.inputs.is_internet == '1'"
            multiple
            clearable
            collapse-tags
            class="w180 knowledge"
            :class="{knowledgeActive: isKnowledge.length > 0}"
            @click="handleKnowledgeClick"
          >
            <el-option-group>
              <el-option-group
                v-for="item in knowledgeList"
                :key="item.value"
                :label="item.label"
              >
                <el-option
                  v-for="ele in item.group"
                  :key="ele.id"
                  :value="ele.id"
                  :label="ele.name"
                />
              </el-option-group>
            </el-option-group>
          </el-select>
          <el-select
            v-if="chatStore.currentAgent.chatType === 'Text2OfficeFile'"
            v-model="chatStore.inputs.OfficeFileType"
            class="w140 model"
          >
            <el-option label="word" value="word" />
          </el-select>
          <el-tag
            :type="chatStore.inputs.is_internet == '1' ? 'primary' : 'info'"
            class="cursor-pointer select-none text-14"
            disable-transitions
            size="large"
            @click="handleNetClick"
          >
            <div style="display: flex; align-items: center;">
              <img :src="chatStore.inputs.is_internet == '1' ? inter : noInter">
              <span style="color:#303133;margin-left:4px" :style="{color: chatStore.inputs.is_internet == '1' ? '#165DFF' :'#303133'}">{{ chatStore.inputs.is_internet == "1" ? "已联网" : "未联网" }}</span>
            </div>

          </el-tag>
        </div>
        <search-skill :disable-file="disableFile" />
        <div class="f-c-c ml-auto">
          <el-tooltip
            v-if="!!chatStore.currentAgent.fileType"
            placement="top"
            :hide-after="0"
            :enterable="false"
            effect="dark"
          >
            <template #content>
              <!-- <div v-if="disableFile" class="font-12 color-white font-bold">
                会话中不支持上传文件
              </div> -->
              <div
                v-if="chatStore.inputs.is_internet == '1'"
                class="font-12 color-white font-bold"
              >
                联网搜索不支持上传文件
              </div>
              <template v-else>
                <div class="font-12 color-white font-bold">
                  文件数量：最多支持{{ chatStore.currentAgent.fileLimit }}个
                </div>
                <div class="font-12 color-white font-bold">
                  支持：{{ chatStore.currentAgent?.fileType || '.pdf, .doc, .xls' }}
                </div>
              </template>
            </template>
            <img
              ref="buttonRef"
              :class="[
                'cursor-pointer w24 h24',
                chatStore.inputs.is_internet == '1' ? 'opacity-50' : '',
              ]"
              :src="file"
              @click="handleUpload"
            >
          </el-tooltip>
          <svg-icon
            v-if="isAnswering"
            icon-class="stop"
            class="ml10 wh24 cursor-pointer color-[--el-color-primary]"
            @click="handleStop"
          />
          <svg-icon
            v-else
            icon-class="send"
            class="ml10 wh24 cursor-pointer color-[--el-color-primary]"
            @click="handleEnter"
          />
        </div>
      </div>
    </div>
    <dialog-temp v-model="tempVisible" />
  </div>
</template>

<style lang="scss" scoped>
.search-com {
  position: relative;
  .add-chat{
    position: absolute;
    left: 50%;
    top: -50px;
    transform: translateX(-50%);
  }
  .search-textarea{
    :deep(.el-textarea__inner) {
      box-shadow: none;
      resize: none;
      padding: 0;
      &.is-focus{
        box-shadow: none;
      }
    }
  }
  ::v-deep(.el-input__wrapper) {
    background-color: transparent;
    box-shadow: none;
    padding-left: 0;
    padding-right: 0;
    .el-input__inner {
      color: #1d2129;
    }
  }
  .func-modules-box {
    display: flex;
    gap: 0 24px;
    .func-modules-item {
      cursor: pointer;
      // padding: 4px 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0 8px;
      height: 36px;
      // border: 1px solid #c8cdd3;
      // border-radius: 8px;
      .func-modules-item-icon {
        display: flex;
        align-items: center;
        img {
          width: 20px;
          height: 20px;
        }
      }
      .func-modules-item-text {
        font-size: 14px;
        color: #1d2129;
      }
    }
  }
}
.func-box {
  margin-top: 10px;
  .func-box-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .func-text{
      width: 86px;
      height: 32px;
      background: #165DFF;
      text-align: center;
      line-height: 32px;
      color: #fff;
      font-weight: 500;
      font-family: PingFang SC;
      display: flex;
      align-items: center;
      justify-content: center;
      gap:0 4px;
      border-radius: 8px;
    }
  }
}

.el-tag{
  border: none;
  border-radius: 8px;
}
</style>
