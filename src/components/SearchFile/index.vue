<script setup name="SearchFile">
import { useVModel } from '@vueuse/core'
import trash from '@/assets/images/svg/icon_trash.svg'
import doc from '@/assets/icons/svg/doc/doc.svg'
import docx from '@/assets/icons/svg/doc/docx.svg'
import jpg from '@/assets/icons/svg/doc/jpg.svg'
import pdf from '@/assets/icons/svg/doc/pdf.svg'
import png from '@/assets/icons/svg/doc/png.svg'
import ppt from '@/assets/icons/svg/doc/ppt.svg'
import pptx from '@/assets/icons/svg/doc/pptx.svg'
import txt from '@/assets/icons/svg/doc/txt.svg'
import xls from '@/assets/icons/svg/doc/xls.svg'
import xlsx from '@/assets/icons/svg/doc/xlsx.svg'
import useChatStore from '@/store/modules/chat'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: { // 只读模式，在聊天正文中开启
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue'])
const files = useVModel(props, 'modelValue', emit)
const chatStore = useChatStore()

// 删除文件
const handleDelete = (i) => {
  chatStore.removeSingleFile(files.value[i].url)
  files.value.splice(i, 1)
}
// 格式化文件大小
const formatSize = (size) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB'
  } else {
    return (size / 1024 / 1024).toFixed(2) + 'MB'
  }
}
// 根据文件后缀名获取对应的文件图标
const getFileIcon = (name) => {
  const ext = name?.split('.').pop().toLowerCase()
  const icons = {
    doc: doc,
    docx: docx,
    jpg: jpg,
    jpeg: jpg,
    pdf: pdf,
    png: png,
    ppt: ppt,
    pptx: pptx,
    txt: txt,
    xls: xls,
    xlsx: xlsx
  }
  return icons[ext] || doc
}
// 预览文件
const handlePrevew = (url) => {
  if (!url) return
  window.open(url)
  // if (url.startsWith('http')) {
  //   window.open(url)
  //   return
  // }
  // window.open(import.meta.env.VITE_APP_BASE_API + '/dify' + url)
}
</script>

<template>
  <div v-if="files?.length" class="search-file overflow-x-auto flex border-b-1px border-b-solid border-#E5E6EB py-8px">
    <div
      v-for="item, index in files"
      :key="item.name"
      class="file-item flex items-center border-1px border-solid border-#C9CDD4 px-8 py-4 bg-#F7F8FA border-rd-4px cursor-pointer position-relative"
      @click="handlePrevew(item.url)"
    >
      <div class="mr-12">
        <img class="w24 h24" :src="getFileIcon(item.name || item.filename)" alt="">
      </div>
      <div class="ellipsis flex flex-col justify-between mr-7">
        <div class="ellipsis text-12px color-#1D2129 line-height-20">{{ item.name || item.filename }}</div>
        <div class="text-12px color-#86909C line-height-18">{{ formatSize(item.size) }}</div>
      </div>
      <div v-if="!readonly" class="w-16 h-16 f-c-c cursor-pointer ml-auto">
        <img
          class="cursor-pointer"
          :src="trash"
          alt=""
          @click.stop="handleDelete(index)"
        >
      </div>
      <div v-if="item.status === 'loading'" class="position-absolute top-0 left-0 w-full h-full bg-#ffffff99 f-c-c pointer-events-none">
        <el-icon class="is-loading" :size="24" color="#409efc">
          <Loading />
        </el-icon>
      </div>
      <div v-if="item.status === 'error'" class="position-absolute top-0 left-0 w-full h-full bg-#ffffffcc f-c-c pointer-events-none text-14 color-red font-bold">
        上传失败，请重新上传
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.file-item{
  width: 246px;
  margin-right: 10px;
  // &:nth-of-type(3n-1),
  // &:nth-of-type(3n-2){
  //   margin-right: 10px;
  // }
}
</style>
