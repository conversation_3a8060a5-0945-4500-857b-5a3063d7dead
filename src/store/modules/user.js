/* eslint-disable eqeqeq */
import { login, logout, getInfo, loginTel } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import defAva from '@/assets/images/profile.jpg'
import request from '@/utils/request'
import { emitter } from '@/plugins'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      name: '',
      avatar: '',
      roles: [],
      permissions: [],
      userInfo: {},
      knowledgeLibs: [], // 知识库列表
      isLoggedIn: false
    }),
    actions: {
      // 登录
      login(userInfo) {
        const username = userInfo.username.trim()
        const password = userInfo.password
        const code = userInfo.code
        const uuid = userInfo.uuid
        return new Promise((resolve, reject) => {
          login(username, password, code, uuid).then(res => {
            setToken(res.token)
            this.token = res.token
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },

      LoginTel(userInfo) {
        console.log(userInfo)
        const tel = userInfo.tel.trim()
        const code = userInfo.code
        return new Promise((resolve, reject) => {
          loginTel(tel, code).then(res => {
            this.isLoggedIn = true
            setToken(res.token)
            emitter.emit('refreshToken')
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            const user = res.user
            const avatar = (user.avatar == '' || user.avatar == null) ? defAva : import.meta.env.VITE_APP_BASE_API + user.avatar

            if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
              this.roles = res.roles
              this.permissions = res.permissions
              this.userInfo = res.user
              localStorage.setItem('Authorization', user.zs2)
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.name = user.userName
            this.avatar = avatar
            resolve(res)
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取知识库列表
      getKnowlegeLib() {
        Promise.all([
          // 获取团队知识库
          request({
            url: '/cms/knowledgebase/list',
            method: 'get',
            params: {
              permission: 'team',
              shareHandleStatus: 'agree'
            }
          }),
          // 获取个人知识库
          request({
            url: '/cms/knowledgebase/list',
            method: 'get',
            params: {
              permission: 'me',
              shareHandleStatus: 'agree'
            }
          }),
          // 获取共享知识库
          request({
            url: '/cms/knowledgebase/list',
            method: 'get',
            params: {
              permission: 'share',
              shareHandleStatus: 'agree'
            }
          })
        ]).then(([teamRes, personalRes, sharedRes]) => {
          const knowledgeLibs = []

          // 处理团队知识库
          const teamList = []
          if (Array.isArray(teamRes)) {
            teamRes.forEach(item => {
              teamList.push({ ...item,
                shareStatus: item.shareStatus || false })
            })
          }

          // 处理个人知识库
          const meList = []
          if (Array.isArray(personalRes)) {
            personalRes.forEach(item => {
              meList.push({
                ...item,
                shareStatus: item.shareStatus || false
              })
            })
          } else if (personalRes.code === 0 && Array.isArray(personalRes.data)) {
            personalRes.data.forEach(item => {
              meList.push({
                ...item,
                shareStatus: item.shareStatus || false
              })
            })
          }

          // 处理共享知识库
          const shareList = []
          if (Array.isArray(sharedRes)) {
            sharedRes.forEach(item => {
              shareList.push({
                ...item,
                permission: item.permission || 'share',
                shareHandleStatus: item.shareHandleStatus || ''
              })
            })
          } else if (sharedRes.code === 0 && Array.isArray(sharedRes.data)) {
            sharedRes.data.forEach(item => {
              shareList.push({
                ...item,
                permission: item.permission || 'share',
                shareHandleStatus: item.shareHandleStatus || ''
              })
            })
          }

          // 组装知识库列表
          if (meList.length) {
            knowledgeLibs.push({
              label: '个人知识库',
              group: meList
            })
          }

          if (teamList.length) {
            knowledgeLibs.push({
              label: '团队知识库',
              group: teamList
            })
          }

          if (shareList.length) {
            knowledgeLibs.push({
              label: '共享知识库',
              group: shareList
            })
          }

          this.knowledgeLibs = knowledgeLibs
        }).catch(error => {
          console.error('获取知识库列表失败:', error)
          this.knowledgeLibs = []
        })
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.afterLogout()
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      afterLogout() {
        this.token = ''
        this.roles = []
        this.knowledgeLibs = []
        this.permissions = []
        this.isLoggedIn = false
        removeToken()
        emitter.emit('refreshToken')
        localStorage.removeItem('Authorization')
        localStorage.removeItem('isKnowledge')
        localStorage.removeItem('model')
        localStorage.removeItem('is_internet')
      }
    }
  })

export default useUserStore
