import request from '@/utils/request'
import { agentList } from '@/assets/dict'

const defaultInputs = Object.freeze({
  query: '',
  ChatType: 'Chat',
  LanguageType: '英语',
  MusicStyle: '流行',
  OfficeFileType: 'word',
  KnowledgeIds: '',
  Authorization: '',
  is_internet: 0,
  model: 'DeepSeek',
  FileUpload: null
})

const useChatStore = defineStore(
  'chat',
  {
    state: () => ({
      modelList: [{
        key: 'DeepSeek',
        value: 'DeepSeek'
      }],
      inputs: {
        ...defaultInputs
      },
      chatFiles: [],
      pptToken: ''
    }),
    getters: {
      currentAgent() {
        return agentList.find(item => item.chatType === this.inputs.ChatType) || {}
      }
    },
    actions: {
      // 重置inputs参数
      resetInputs() {
        this.removeFileUpload()
        this.inputs = {
          ...defaultInputs
        }
      },
      resetChatFiles(files) {
        this.chatFiles = []
      },
      setPPtToken(params) {
        this.pptToken = params
      },
      clearPPtToken() {
        this.pptToken = null
      },
      // 接口获取模型列表
      getModelList() {
        request({
          url: '/cms/model/status',
          method: 'get'
        }).then(res => {
          this.modelList = res.data?.map(item => ({
            label: item.dictLabel,
            value: item.dictValue,
            disabled: item.status !== '0'
          }))
        })
      },
      // 单独删除文件
      removeSingleFile(url) {
        request({
          url: '/cms/common/deleteFile',
          method: 'delete',
          data: {
            key: url
          }
        })
      },
      // 删除上传文件
      removeFileUpload() {
        if (this.inputs.FileUpload) { // 还是加个判断吧
          this.removeSingleFile(this.inputs.FileUpload)
          this.inputs.FileUpload = null
        }
      }
    }
  })

export default useChatStore
