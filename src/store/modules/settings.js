import defaultSettings from '@/settings'
import { useDynamicTitle } from '@/utils/dynamicTitle'
import { requireImg } from '@/utils'

const { sideTheme, showSettings, topNav, tagsView, fixedHeader, sidebarLogo, dynamicTitle } = defaultSettings

const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || ''

const logoMap = {
  dark: requireImg('logo/logo_dark.png'),
  blue: requireImg('logo/logo_blue.png')
}
const loginBgMap = {
  dark: requireImg('images/svg/image_login.png'),
  blue: requireImg('images/svg/image_login_blue.png')
}

const useSettingsStore = defineStore(
  'settings',
  {
    state: () => ({
      title: '',
      theme: 'blue',
      // theme: storageSetting.theme || 'blue',
      sideTheme: storageSetting.sideTheme || sideTheme,
      showSettings: showSettings,
      topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
      tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
      fixedHeader: storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,
      sidebarLogo: storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,
      dynamicTitle: storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle
    }),
    getters: {
      logoSrc(state) {
        return logoMap[state.theme]
      },
      loginBgSrc(state) {
        return loginBgMap[state.theme]
      }
    },
    actions: {
      // 修改布局设置
      changeSetting(data) {
        const { key, value } = data
        // eslint-disable-next-line no-prototype-builtins
        if (this.hasOwnProperty(key)) {
          this[key] = value
        }
      },
      // 设置网页标题
      setTitle(title) {
        this.title = title
        useDynamicTitle()
      }
    }
  })

export default useSettingsStore
