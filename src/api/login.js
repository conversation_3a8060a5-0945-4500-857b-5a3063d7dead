import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/cms/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/cms/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/cms/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

export function getSmsCode(data) {
  return request({
    url: '/cms/appCaptcha',
    data,
    headers: {
      isToken: false
    },
    method: 'post',
    timeout: 20000
  })
}

export function loginTel(tel, code) {
  const data = {
    tel,
    code
  }
  return request({
    url: '/cms/loginTel',
    headers: {
      isToken: false
    },
    method: 'post',
    data
  })
}
