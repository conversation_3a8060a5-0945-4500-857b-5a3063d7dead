import request from '@/utils/request'

// 共享知识库
export function shareKnowledgeBase(data) {
  return request({
    url: '/knowledge/api/v1/share',
    method: 'post',
    data
  })
}

// 获取共享知识库列表
export function getSharedKnowledgeList() {
  return request({
    url: '/knowledge/api/v1/share/list',
    method: 'get'
  })
}

// 同意加入知识库
export function acceptShare(data) {
  return request({
    url: '/knowledge/api/v1/share/accept',
    method: 'post',
    data
  })
}

// 拒绝加入知识库
export function rejectShare(data) {
  return request({
    url: '/knowledge/api/v1/share/reject',
    method: 'post',
    data
  })
}

// 退出共享知识库
export function exitShare(data) {
  return request({
    url: '/knowledge/api/v1/share/exit',
    method: 'post',
    data
  })
}
