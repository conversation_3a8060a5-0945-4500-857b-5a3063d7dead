<template>
  <div class="login">
    <img src="@/assets/images/svg/icon_logo.svg" class="login-logo">
    <el-form
      ref="loginRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
    >
      <div class="title">
        欢迎登录
        <div class="sub-title">墨麟AI平台</div>
      </div>
      <!-- <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          text
          size="large"
          auto-complete="off"
          placeholder="账号"
        >
          <template #prefix>
            <svg-icon
              icon-class="user"
              class="el-input__icon input-icon"
            />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter="handleLogin"
        >
          <template #prefix>
            <svg-icon
              icon-class="password"
              class="el-input__icon input-icon"
            />
          </template>
        </el-input>
      </el-form-item> -->
      <el-form-item prop="tel">
        <el-input
          v-model="loginForm.tel"
          text
          auto-complete="off"
          placeholder="请输入账号"
        >
          <template #prefix>
            <!-- <svg-icon icon-class="phone" class="el-input__icon input-icon" /> -->
            <img src="@/assets/images/svg/icon_login_user.svg" alt="">
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          text
          auto-complete="off"
          placeholder="验证码"
          @keyup.enter="handleLogin"
        >
          <template #prefix>
            <svg-icon icon-class="validCode" class="el-input__icon input-icon" />
          </template>

          <template #suffix>
            <el-button

              size="mini"
              :loading="smsLoading"
              @click="getSmsCode1"
            >
              {{ smsDone ? `${smsTime}秒后重试` : smsLoading ? '验证码获取中' : '发送验证码' }}
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        v-if="captchaEnabled"
        prop="code"
      >
        <el-input
          v-model="loginForm.code"
          size="large"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter="handleLogin"
        >
          <template #prefix>
            <svg-icon
              icon-class="validCode"
              class="el-input__icon input-icon"
            />
          </template>
        </el-input>
        <div class="login-code">
          <img
            :src="codeUrl"
            class="login-code-img"
            @click="getCode"
          >
        </div>
      </el-form-item>
      <!-- <el-checkbox
        v-model="loginForm.rememberMe"
        style="margin:0px 0px 25px 0px;"
      >
        记住密码
      </el-checkbox> -->
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="large"
          type="primary"

          style="width:100%;background-color:#1D2129;color:#E8E8E9"
          @click.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div
          v-if="register"
          style="float: right;"
        >
          <router-link
            class="link-type"
            :to="'/register'"
          >
            立即注册
          </router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <!-- <div class="el-login-footer">
      <span class="record">
        <img src="/src/assets/logo/record-keep.png">
        <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50010602504718" target="_blank">渝公网安备50010602504718</a>
      </span>
      <span style="margin-left:20px" class="record">
        <a href="https://beian.miit.gov.cn/" target="_blank">渝ICP备2024030301号-2</a>
      </span>
    </div> -->
  </div>
</template>

<script setup>
import { getCodeImg, getSmsCode } from '@/api/login'
import Cookies from 'js-cookie'
import { decrypt } from '@/utils/jsencrypt'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const router = useRouter()
const { proxy } = getCurrentInstance()
// 手机验证码获取请求中
const smsLoading = ref(false)
// 验证码获取完成
const smsDone = ref(false)

const smsTime = ref(120)
const loginForm = ref({
  username: '',
  password: '',
  rememberMe: true,
  code: '',
  uuid: ''
})

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
}

const codeUrl = ref('')
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(false)
// 注册开关
const register = ref(false)
const redirect = ref(undefined)

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    console.log(valid)
    if (valid) {
      loading.value = true
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      // if (loginForm.value.rememberMe) {
      //   Cookies.set('username', loginForm.value.username, { expires: 30 })
      //   Cookies.set('password', encrypt(loginForm.value.password), { expires: 30 })
      //   Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 })
      // } else {
      //   // 否则移除
      //   Cookies.remove('username')
      //   Cookies.remove('password')
      //   Cookies.remove('rememberMe')
      // }
      // 调用action的登录方法

      userStore.LoginTel(loginForm.value).then((res) => {
        console.log('接口调用成功', res)
        router.push({ path: redirect.value || '/' })
      }).catch((err) => {
        console.log('调用接口失败', err)
        loading.value = false
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode()
        }
      })
    } else {
      proxy.$modal.msgError('请输入正确手机号')
    }
  })
}

// 获取验证码
function getSmsCode1() {
  getSmsCode({
    phone: loginForm.value.tel
  }).then(() => {
    smsLoading.value = true
    setTimeout(() => {
      smsDone.value = true
      smsCount()
    }, 1000)
  })
  // }
  // })
}
function smsCount() {
  if (smsTime.value > 0) {
    setTimeout(() => {
      smsTime.value--
      smsCount()
    }, 1000)
  } else {
    smsTime.value = 120
    smsLoading.value = false
    smsDone.value = false
  }
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = 'data:image/gif;base64,' + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

function getCookie() {
  const username = Cookies.get('username')
  const password = Cookies.get('password')
  const rememberMe = Cookies.get('rememberMe')
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  }
}

// getCode()
getCookie()
</script>

<style lang="scss" scoped>
.login-dialog {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 800px;
  width: 1200px;
}

.login-left {
  width: 700px;
  height: 800px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form {
  width: 360px;
}

.title {
  margin: 0px auto 30px auto;
  font-size: 24px;
  line-height: 44px;
  font-weight: 600;
  color: rgba(0,0,0,.9);
  text-align: center;
}

.sub-title {
  font-size: 16px;
  color: #707070;
  font-weight: normal;
}

:deep(.el-dialog) {
  border-radius: 8px;
  margin-top: 8vh !important;
  padding: 0; // 移除默认的 padding

  .el-dialog__header {
    padding: 0;
    margin: 0;
  }

  .el-dialog__body {
    padding: 0; // 移除默认的 padding
  }
}

:deep(.el-dialog__headerbtn) {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;

  .el-dialog__close {
    color: #fff;
    font-size: 20px;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.login-form {
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
}

:deep(.el-dialog__header){
  padding-bottom: 0px;
}

.el-form-item {
  margin-bottom: 20px;
}
</style>
