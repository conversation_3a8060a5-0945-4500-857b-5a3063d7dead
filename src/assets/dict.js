export const modelList = [
  {
    value: 'DeepSeek',
    label: 'DeepSeek'
  },
  {
    value: 'QwQ',
    label: '通义千问'
  }
]

export const agentList = [{
  icon: 'zndh',
  // title: '智能对话',
  hiddenNav: true,
  chatType: 'Chat',
  fileType: '.pdf, .doc, .docx, .xls, .xlsx, .txt',
  fileLimit: 10,
  // tempList: [{
  //   title: '总结汇报',
  //   info: '凝练你的工作成效',
  //   content: '我是一名【输入职业】 ，帮我写一份关于【目的：项目进展总结、团队工作成果或其他】的总结汇报，需要包含【项目进展总结】、【工作内容与成果】和【成果】。'
  // }, {
  //   title: '心得体会',
  //   info: '助你提炼归纳所感所悟',
  //   content: '我是一名【输入职业】，帮我写一份关于【主题】的心得体会。'
  // }, {
  //   title: '思想汇报',
  //   info: '进行反思梳理和深入总结',
  //   content: '我是一名【输入职业】，帮我写一份关于【主题】的思想汇报。'
  // }, {
  //   title: '会议纪要',
  //   info: '高效整理会议要点',
  //   content: '帮我写一个讨论【主题】周会 会议纪要，语气需要正式的，需要包含本周会议的主要讨论内容、核心结论以及后续计划，字数在 【输入数字】字左右。'
  // }, {
  //   title: '教案',
  //   info: '打造多样化的教学方案',
  //   content: '帮我写一份教案，授课对象是【年龄学段】，主题是【课程名称】。'
  // }, {
  //   title: '文字润色',
  //   info: '让文字表达更出彩',
  //   content: '帮我对以下内容进行润色，润色要求【输入要求】语言风格幽默 ，润色字数限制为【输入数字】 ：【这里输入要优化的内容】。'
  // }, {
  //   title: '文字校对',
  //   info: '保证内容无误无漏',
  //   content: '帮我对以下内容进行校对并修正：【这里输入要优化的内容】。'
  // // }, {
  // //   title: '标书制作',
  // //   info: '快速输出标书框架',
  // //   content: '我是一名【投标专员】，帮我根据【上传的文件要求】写一份投标响应文件，要求以【关联的知识库格式】进行编写。'
  // // }, {
  // //   title: '解决方案',
  // //   info: '快速搭建方案架构',
  // //   content: '项目背景：【停车场监控建设】，项目要求：【100个监控点位，30天存储，手机端和电脑端均可实时查看监控画面】。帮我根据项目背景、项目要求写一份【初次与客户交流】的【解决方案】，要求以【WORD格式】输出。项目背景：【停车场监控建设】，项目要求：【100个监控点位，30天存储，手机端和电脑端均可实时查看监控画面】。帮我根据项目背景、项目要求写一份【建设清单】的【报价方案】，要求以【表格形式】输出。'
  // }]
}, {
  icon: 'wst',
  title: '文生图',
  info: '妙笔生花绘图，创意无限',
  chatType: 'Text2Img',
  tempList: [{
    title: '简化配图',
    info: '快速生成配图',
    content: '帮我生成【用于红色教育培训PPT的背景配图】'
  }, {
    title: '精准配图',
    info: '精准生成配图',
    content: '帮我生成一幅画，【插画】，【小女孩坐在一个小船上带着斗笠，皮肤干净大眼睛，面带微笑，周围很大荷叶，荷叶的姿态明暗虚实大小都有变化很丰富，以手绘的笔触感绘制，水面有小女孩倒影和锦鲤，主体和高光色彩明亮偏暖，画面刻画精细，整体协调富有美感】，【画面俯视】。'
  }]
}, {
  icon: 'wsppt',
  title: '文生PPT',
  info: '文生PPT，高效展示',
  chatType: 'Text2PPT',
  tempList: [{
    title: '汇报演讲',
    info: '智能生成汇报演示',
    content: '帮我写一份主题为【主题】的参赛演讲PPT，风格酷炫，内容需包括【背景】、【做法】、【亮点】。'
  }, {
    title: '方案输出',
    info: '快速输出方案汇报',
    content: '项目背景：【停车场监控建设】，项目要求：【100个监控点位，30天存储，手机端和电脑端均可实时查看监控画面】，帮我根据项目背景、项目要求写一份【初次与客户交流】的【解决方案】，要求包含【项目背景、需求分析、建设方案、预计成效】，以【科技】风格呈现。'
  }]
}, {
  icon: 'wssp',
  title: '文生视频',
  info: '文生视频，生动呈现',
  chatType: 'Text2Video',
  tempList: [{
    title: '简化视频',
    info: '简单生成视频',
    content: '摄像机平移，小男孩喝咖啡。'
  }, {
    title: '精准视频',
    info: '精细化生成视频',
    content: '摄影机平移（镜头移动），一个小男孩坐在公园的长椅上（主体描述），手里拿着一杯热气腾腾的咖啡（主体动作）。他穿着一件蓝色的衬衫，看起来很偷快（主体细节描述），背景绿树成荫的公园，阳光透过树叶洒在男孩身上（所处环境描述）。'
  }]
}, {
  icon: 'tssp',
  title: '图生视频',
  info: '图生视频，精彩纷呈',
  chatType: 'Img2Video',
  fileType: '.png, .jpg, .jpeg, .bmp, .webp',
  fileLimit: 1,
  tempList: [{
    title: '简化视频',
    info: '让图片简单动起来',
    content: '让上传的图片【动起来】。'
  }, {
    title: '精准视频',
    info: '让图片精细化动起来',
    content: '让图片中的【人物】【在大草原上】【疯狂奔跑】。'
  }]
}, {
  icon: 'tpsb',
  title: 'OCR识别',
  info: '精准识别图片文字',
  chatType: 'OcrImg',
  fileType: '.png, .jpg, .jpeg, .bmp, .webp',
  fileLimit: 1,
  tempList: [{
    title: '表格识别',
    info: '快速识别表格输出',
    content: '识别上传图片，以【表格形式输出】，要求包含【序号】、【内容1】、【内容2】'
  }, {
    title: '图片内容识别',
    info: '识别图片整理输出',
    content: '提取上传图片中的内容，以【WORD形式】进行输出'
  }]
}, {
  icon: 'wtsb',
  title: '图像识别',
  info: '智能识别图像内容',
  chatType: 'IdentifyImg',
  fileType: '.png, .jpg, .jpeg, .bmp, .webp',
  fileLimit: 1,
  tempList: [{
    title: '图像内容总结',
    info: '总结图像内容',
    content: '描述一下上传图像中所表达的【场景】、【意图】'
  }]
// }, {
//   icon: 'xz',
//   title: '作业批改',
//   info: '自动批改作业，高效准确',
//   chatType: 'CheckJob',
//   fileType: '.png, .jpg, .jpeg, .bmp, .webp',
//   fileLimit: 1,
//   tempList: [{
//     title: '答案输出',
//     info: '快速输出答案',
//     content: '列出上传图片中【所有题目的正确答案】，以【表格】/【WORD】形式输出'
//   }, {
//     title: '答案纠错',
//     info: '智能纠正错误',
//     content: '列出上传图片中【回答错误的题目】，以【表格】形式输出，需包括【序号】、【题目】、【已作答案】、【正确答案】'
//   }]
}, {
  icon: 'fy',
  title: '在线翻译',
  info: '实时翻译，沟通无界',
  chatType: 'Translate',
  skills: [{
    prop: 'LanguageType',
    options: [{
      value: '英语',
      label: '英语'
    }, {
      value: '中文',
      label: '中文'
    }, {
      value: '法语',
      label: '法语'
    }, {
      value: '德语',
      label: '德语'
    }, {
      value: '俄语',
      label: '俄语'
    }, {
      value: '日语',
      label: '日语'
    }, {
      value: '韩语',
      label: '韩语'
    }]
  }]
}, {
  icon: 'wsyy',
  title: '文生音乐',
  info: '把想法变成音乐',
  chatType: 'Text2Music',
  skills: [{
    prop: 'model',
    options: [{
      value: 'DeepSeek',
      label: 'DeepSeek'
    }]
  }, {
    prop: 'MusicStyle',
    options: [{
      value: '流行',
      label: '流行'
    }, {
      value: '都市',
      label: '都市'
    }, {
      value: '摇滚',
      label: '摇滚'
    }, {
      value: '嘻哈',
      label: '嘻哈'
    }, {
      value: '电子',
      label: '电子'
    }, {
      value: '古典',
      label: '古典'
    }, {
      value: 'R&B',
      label: 'R&B'
    }, {
      value: 'Disco',
      label: 'Disco'
    }, {
      value: '金属',
      label: '金属'
    }, {
      value: '雷鬼',
      label: '雷鬼'
    }, {
      value: '蓝调',
      label: '蓝调'
    }, {
      value: '爵士',
      label: '爵士'
    }, {
      value: '民谣',
      label: '民谣'
    }, {
      value: '乡村',
      label: '乡村'
    }, {
      value: '实验',
      label: '实验'
    }, {
      value: '戏曲',
      label: '戏曲'
    }, {
      value: '民族',
      label: '民族'
    }, {
      value: '世界',
      label: '世界'
    }]
  }]
}, {
  icon: 'wdsc',
  title: '文档生成',
  info: '多种格式，一键成文',
  chatType: 'Text2OfficeFile',
  fileType: '.pdf, .doc, .docx, .xls, .xlsx, .txt',
  fileLimit: 10,
  tempList: [{
    title: '总结汇报',
    info: '凝练你的工作成效',
    content: '我是一名【输入职业】 ，帮我写一份关于【目的：项目进展总结、团队工作成果或其他】的总结汇报，需要包含【项目进展总结】、【工作内容与成果】和【成果】。'
  }, {
    title: '心得体会',
    info: '助你提炼归纳所感所悟',
    content: '我是一名【输入职业】，帮我写一份关于【主题】的心得体会。'
  }, {
    title: '思想汇报',
    info: '进行反思梳理和深入总结',
    content: '我是一名【输入职业】，帮我写一份关于【主题】的思想汇报。'
  }, {
    title: '会议纪要',
    info: '高效整理会议要点',
    content: '帮我写一个讨论【主题】周会 会议纪要，语气需要正式的，需要包含本周会议的主要讨论内容、核心结论以及后续计划，字数在 【输入数字】字左右。'
  }, {
    title: '教案',
    info: '打造多样化的教学方案',
    content: '帮我写一份教案，授课对象是【年龄学段】，主题是【课程名称】。'
  }]
}, {
  icon: 'sjfx',
  title: '数据分析',
  info: '对表格数据进行分析和处理',
  chatType: 'DataAnalysis',
  fileType: '.xls, .xlsx',
  fileLimit: 10,
}, {
  icon: 'wsszr',
  title: '数字人',
  info: '实时交互拟真形象实时交互拟真形象',
  chatType: 'DigitalPerson',
  externalUrl: `${location.protocol}//${location.hostname === 'localhost' ? '***************' : location.hostname}:3001` //'http://***************:3001'
}]