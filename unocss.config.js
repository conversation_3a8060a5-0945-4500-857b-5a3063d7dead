import { defineConfig, presetAttributify, presetUno } from 'unocss'
import presetRemToPx from '@unocss/preset-rem-to-px'

/** https://unocss.dev/interactive/ */
/** https://to-unocss.netlify.app/ */
export default defineConfig({
  exclude: ['node_modules', '.git', '.github', '.husky', '.vscode', 'build', 'dist', 'mock', 'public', './stats.html'],
  presets: [
    presetUno(),
    presetAttributify(),
    presetRemToPx({
      // 将默认的rem单位改为4px
      baseFontSize: 4
    })
  ],
  shortcuts: [
    ['wh-full', 'w-full h-full'],
    ['f-c-c', 'flex justify-center items-center'],
    ['f-c-r', 'flex justify-around items-center'],
    ['f-c-b', 'flex justify-between items-center'],
    ['f-c-c-b', 'flex justify-between items-center flex-col'],
    ['f-c-c-c', 'flex justify-center items-center flex-col'],
    ['f-c-col', 'flex justify-center flex-col'],
    ['f-ci-col', 'flex items-center flex-col'],
    ['f-c', 'flex items-center'],
    ['f-e', 'flex flex-items-end'],
    ['flex-col', 'flex flex-col'],
    ['absolute-lt', 'absolute left-0 top-0'],
    ['absolute-lb', 'absolute left-0 bottom-0'],
    ['absolute-rt', 'absolute right-0 top-0'],
    ['absolute-rb', 'absolute right-0 bottom-0'],
    ['absolute-center', 'absolute-lt f-c-c wh-full'],
    ['text-ellipsis', 'truncate']
  ],
  rules: [
    [/^wh-?(.+)$/, ([, w]) => ({ width: w + 'px', height: w + 'px' })],
    [/^bc-(.+)$/, ([, color]) => ({ 'border-color': `#${color}` })],
    ['card-shadow', { 'box-shadow': '0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017' }],
    [/^bgi-\[([\w\W]+)\]$/, ([_, w]) => {
      const path = `@/assets`
      return ({ 'background-image': `url('${path}/${w}')` })
    }]
  ],
  theme: {
    colors: {
      primary: 'var(--primary-color)',
      primary_hover: 'var(--primary-color-hover)',
      primary_pressed: 'var(--primary-color-pressed)',
      primary_active: 'var(--primary-color-active)',
      info: 'var(--info-color)',
      info_hover: 'var(--info-color-hover)',
      info_pressed: 'var(--info-color-pressed)',
      info_active: 'var(--info-color-active)',
      success: 'var(--success-color)',
      success_hover: 'var(--success-color-hover)',
      success_pressed: 'var(--success-color-pressed)',
      success_active: 'var(--success-color-active)',
      warning: 'var(--warning-color)',
      warning_hover: 'var(--warning-color-hover)',
      warning_pressed: 'var(--warning-color-pressed)',
      warning_active: 'var(--warning-color-active)',
      error: 'var(--error-color)',
      error_hover: 'var(--error-color-hover)',
      error_pressed: 'var(--error-color-pressed)',
      error_active: 'var(--error-color-active)',
      dark: '#18181c'
    }
  }
})
