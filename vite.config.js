import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.lonely.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.lonely.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 5313,
      host: true,
      proxy: {
        '/dev-api/xfyun': {
          // target: 'http://***************:8037/',
          target: 'http://***************:18088/prod-api/xfyun',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api\/xfyun/, '')
        },
        '/dev-api/cms': {
          // target: 'http://***************:8037/',
          target: 'http://***************:18088/prod-api/cms',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api\/cms/, '')
        },
        '/dev-api/knowledge': {
          target: 'http://***************:18088/prod-api/knowledge',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api\/knowledge/, '')
        },
        // https://cn.vitejs.dev/config/#server-proxy
        '/dev-api/dify': {
          // target: 'http://***************:8077',
          // target: 'http://*************:7001',
          target: 'http://***************:18088/prod-api/dify',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api\/dify/, '')
        },
        // https://cn.vitejs.dev/config/#server-proxy
        '/files': {
          // target: 'http://***************:8077',
          target: 'http://***************:18088/prod-api/dify',
          changeOrigin: true
        },
        // https://cn.vitejs.dev/config/#server-proxy
        '/spbai': {
          // target: 'http://***************:8077',
          target: 'http://oss.feryun.cn',
          changeOrigin: true
        },
        '/profile': {
          target: 'http://***************:18088',
          changeOrigin: true
        }
      }
    },
    // fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              }
            }
          }
        ]
      }
    }
  }
})
